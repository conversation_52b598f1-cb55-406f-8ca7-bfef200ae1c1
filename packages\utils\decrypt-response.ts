import * as dotenv from 'dotenv';
import * as path from 'node:path';
import * as fs from 'node:fs';
import { decryptData, CRYPTO_CONSTANTS } from './lib/crypto';

// 加载环境变量，优先从 .env.local 加载
const rootDir = path.resolve(__dirname, '../..');
const envLocalPath = path.join(rootDir, '.env.local');
const envPath = path.join(rootDir, '.env');

// 手动加载环境变量
if (fs.existsSync(envLocalPath)) {
  console.log(`加载环境变量从 ${envLocalPath}`);
  const envConfig = dotenv.parse(fs.readFileSync(envLocalPath));
  for (const k in envConfig) {
    process.env[k] = envConfig[k];
  }
} else if (fs.existsSync(envPath)) {
  console.log(`加载环境变量从 ${envPath}`);
  dotenv.config({ path: envPath });
} else {
  console.log('未找到环境变量文件');
}

// 输出当前环境变量信息（不显示完整内容，只显示是否存在）
console.log('环境变量检查:');
console.log(`- ${CRYPTO_CONSTANTS.ENV_API_KEY}: ${process.env[CRYPTO_CONSTANTS.ENV_API_KEY] ? '已设置' : '未设置'}`);
console.log(`- ${CRYPTO_CONSTANTS.ENV_API_SECRET}: ${process.env[CRYPTO_CONSTANTS.ENV_API_SECRET] ? '已设置' : '未设置'}`);
console.log(`- ${CRYPTO_CONSTANTS.ENV_API_IV}: ${process.env[CRYPTO_CONSTANTS.ENV_API_IV] ? '已设置' : '未设置'}`);

// 从命令行参数获取加密数据或使用内置的示例数据
let encryptedData: string;

// 检查是否有命令行参数
if (process.argv.length > 2) {
  // 从命令行参数获取文件路径或加密字符串
  const arg = process.argv[2];
  
  // 如果参数看起来像文件路径
  if (fs.existsSync(arg)) {
    console.log(`从文件读取加密数据: ${arg}`);
    encryptedData = fs.readFileSync(arg, 'utf8').trim();
  } else {
    console.log('使用命令行参数作为加密数据');
    encryptedData = arg;
  }
} else {
  // 使用内置的示例数据 - 把你的加密数据放在这里
  console.log('使用内置的示例数据');
  encryptedData = "Axjs3ZhxvJbAeWoXxexUUwtRT7auNe1wAp7VJ3cpY84=";
}

try {
  console.log('\n开始解密数据...');
  
  // 解密数据
  const decryptedString = decryptData(encryptedData);
  
  // 解析JSON
  const parsed = JSON.parse(decryptedString);
  
  console.log('\n解密后的数据:');
  console.log(JSON.stringify(parsed, null, 2));

  // 打印时间戳信息（如果存在）
  if (parsed.timestamp) {
    console.log('\n请求时间戳:', new Date(parsed.timestamp).toISOString());
  }
} catch (error) {
  console.error('数据解密或解析失败:', error);
} 