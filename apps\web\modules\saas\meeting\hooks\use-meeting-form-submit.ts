import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useSession } from "../../auth/hooks/use-session";
import type { MeetingFormValues } from "../utils/meetingFormValidation";
import type { UseFormSetError } from "react-hook-form";

/**
 * 转换表单数据为 API 请求格式
 */
function transformFormDataToApiData(values: MeetingFormValues, userId: string) {
  // 获取当前日期和时间值
  const dateStr = values.date; // 格式：YYYY-MM-DD
  const startTimeStr = values.startTime; // 格式：HH:MM
  
  // 将日期和时间合并并转换为Date对象
  const startDateTime = new Date(`${dateStr}T${startTimeStr}:00`);
  
  // 将结束日期和时间合并并转换为Date对象
  const endTimeStr = values.endTime; // 格式：HH:MM
  const endDateTime = new Date(`${dateStr}T${endTimeStr}:00`);
  
  // 转换为Unix时间戳（秒）
  const startTimeUnix = Math.floor(startDateTime.getTime() / 1000).toString();
  const endTimeUnix = Math.floor(endDateTime.getTime() / 1000).toString();
  
  // 设置会议参数
  const settings = {
    mute_enable_type_join: values.muteParticipantsOnEntry === "muteOff" ? 0 : 
                           values.muteParticipantsOnEntry === "muteOn" ? 1 : 2,
    mute_enable_join: values.muteParticipantsOnEntry !== "muteOff",
    allow_unmute_self: true,
    allow_in_before_host: values.enterInAdvance,
    auto_in_waiting_room: values.waitingRoom,
    allow_screen_shared_watermark: values.screenWatermark,
    water_mark_type: values.screenWatermark ? 0 : undefined,
    auto_record_type: 'cloud',
    participant_join_auto_record: true,
    enable_host_pause_auto_record: true,
    allow_multi_device: values.multiPlatform,
    change_nickname: values.allowParticipantsToRename ? 1 : 2,
    auto_asr: true,
    open_asr_view: 0,
    play_ivr_on_join: values.playIvrOnJoin,
    play_ivr_on_leave: values.playIvrOnLeave,
    only_enterprise_user_allowed: values.onlyEnterpriseUserAllowed,
  };
  
  // 准备创建会议的请求参数
  const meetingData = {
    userid: userId,
    subject: values.title,
    type: 0,
    meeting_type: values.isRecurring ? 1 : 0,
    start_time: startTimeUnix,
    end_time: endTimeUnix,
    settings: settings,
    // 如果是周期性会议，添加重复规则
    ...(values.isRecurring && {
      recurring_rule: {
        recurring_type: values.recurringType,
        until_type: values.untilType,
        ...(values.untilType === 0 && values.untilDate && {
          until_date: Math.floor(new Date(values.untilDate).getTime() / 1000)
        }),
        ...(values.untilType === 1 && values.untilCount && {
          until_count: values.untilCount
        })
      }
    }),
    // 条件性添加参数
    ...(values.requirePassword && values.password && { password: values.password }),
    ...(values.timezone && { time_zone: values.timezone }),
    ...(values.location && { location: values.location }),
    enable_host_key: values.enableHostKey,
    ...(values.enableHostKey && values.hostKey && { host_key: values.hostKey }),
    enable_doc_upload_permission: values.enableDocUpload,
    enable_enroll: values.enable_enroll,
    approval_mode: values.approval_mode,
    // 添加嘉宾参数
    ...(values.guests && values.guests.length > 0 && { guests: values.guests }),
  };
  
  return meetingData;
}

/**
 * 会议表单提交处理 Hook
 */
export function useMeetingFormSubmit(
  organizationSlug: string,
  shouldShowGuestFeature: boolean,
  setInfoDialogOpen: (open: boolean) => void
) {
  const router = useRouter();
  const { user } = useSession();

  const handleSubmit = async (
    values: MeetingFormValues,
    setError: UseFormSetError<MeetingFormValues>
  ) => {
    try {
      // 检查用户信息是否存在
      if (!user?.id) {
        setError("root", {
          type: "validation",
          message: "用户信息不存在，请重新登录"
        });
        return;
      }

      // 检查嘉宾功能权限
      if (values.guests && values.guests.length > 0 && !shouldShowGuestFeature) {
        setError("root", {
          type: "validation",
          message: "当前账户类型不支持会议嘉宾功能"
        });
        return;
      }

      // 转换表单数据为 API 格式
      const meetingData = transformFormDataToApiData(values, user.id);
      
      // 发送创建会议请求
      const response = await fetch('/api/meetings/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(meetingData),
      });
      
      const data = await response.json();
      
      if (!response.ok || !data.success) {
        throw data.error;
      }
      
      toast.success("会议创建成功");
      
      // 重定向到会议列表页面
      router.push(`/app/${organizationSlug}/meeting/list`);
    } catch (error: any) {
      if (error.error_info.message === "每月总接口调用次数超过限制。") {
        setInfoDialogOpen(true);
      } else {
        toast.error(error.error_info.message);
      }
    }
  };

  return { handleSubmit };
}
