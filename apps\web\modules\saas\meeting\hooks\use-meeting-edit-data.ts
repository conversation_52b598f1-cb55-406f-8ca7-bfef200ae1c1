import { useState, useEffect, useRef, useMemo } from "react";
import { toast } from "sonner";
import type { MeetingFormValues } from "../utils/meetingFormValidation";

/**
 * 将API返回的会议数据转换为表单数据格式
 */
function convertToFormData(apiData: any): Partial<MeetingFormValues> {
  if (!apiData) return {};
  console.log("会议详情",apiData);

  // 处理时间戳转换为日期和时间格式
  const startDateTime = new Date(Number.parseInt(apiData.start_time) * 1000);
  const endDateTime = new Date(Number.parseInt(apiData.end_time) * 1000);

  // 处理base64编码的时区
  let timezone = 'Asia/Shanghai';
  try {
    timezone = atob(apiData.time_zone || '');
  } catch (e) {
    console.warn('时区解码失败，使用默认时区');
  }

  return {
    title: apiData.subject || "",
    date: startDateTime.toLocaleDateString('sv-SE'),
    startTime: startDateTime.toTimeString().slice(0, 5), // HH:MM格式
    endTime: endDateTime.toTimeString().slice(0, 5), // HH:MM格式
    location: apiData.location || "",
    isRecurring: apiData.meeting_type === 1,
    timezone: timezone,
    password: apiData.password ?? "******", // 密码可能为空
    requirePassword: true, // 默认开启
    muteParticipantsOnEntry: apiData.settings?.mute_enable_type_join === 0 ? "muteOff" :
                            apiData.settings?.mute_enable_type_join === 1 ? "muteOn" : "muteAuto",
    waitingRoom: apiData.settings?.auto_in_waiting_room || false,
    recordMeeting: apiData.settings?.auto_record_type === "cloud",
    multiPlatform: apiData.settings?.allow_multi_device || false,
    enterInAdvance: apiData.settings?.allow_in_before_host || false,
    allowChat: true, // API中没有对应字段，使用默认值
    allowScreenShare: true, // API中没有对应字段，使用默认值
    allowParticipantsToRename: apiData.settings?.change_nickname === 1,
    enableHostKey: apiData.enable_host_key || false,
    hostKey: apiData.host_key ?? "",
    enableDocUpload: apiData.enable_doc_upload_permission || false,
    screenWatermark: apiData.settings?.allow_screen_shared_watermark || false,
    disableScreenshot: false, // API中没有对应字段，使用默认值
    autoTranscribe: apiData.settings?.auto_asr || false,
    playIvrOnJoin: apiData.settings?.play_ivr_on_join || false,
    playIvrOnLeave: apiData.settings?.play_ivr_on_leave || false,
    onlyEnterpriseUserAllowed: apiData.settings?.only_enterprise_user_allowed ||
                              apiData.settings?.only_allow_enterprise_user_join || false,
    recurringType: apiData.recurring_rule?.recurring_type || 0,
    untilType: apiData.recurring_rule?.until_type || 0,
    untilDate: apiData.recurring_rule?.until_date ?
      new Date(apiData.recurring_rule.until_date * 1000).toISOString().split('T')[0] :
      (() => {
        const date = new Date();
        date.setDate(date.getDate() + 7);
        return date.toISOString().split('T')[0];
      })(),
    untilCount: apiData.recurring_rule?.until_count || 7,
    guests: apiData.guests ?? [{ area: "86", phone_number: "", guest_name: "" }],
    enable_enroll: apiData.enable_enroll || true,
    approval_mode: "manual",
  };
}

/**
 * 会议编辑数据获取和初始化 Hook
 */
export function useMeetingEditData(meetingId: string) {
  const [meetingData, setMeetingData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  useEffect(() => {
    const fetchMeetingDetail = async (id: string) => {
      try {
        // 取消之前的请求
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }

        // 创建新的 AbortController
        abortControllerRef.current = new AbortController();

        setIsLoading(true);
        setError(null);

        const response = await fetch(`/api/meetings/details/${id}`, {
          signal: abortControllerRef.current.signal,
        });

        if (!response.ok) {
          throw new Error(
            `获取会议详情失败 (状态码: ${response.status})`,
          );
        }

        const data = await response.json();
        setMeetingData(data.data.meeting_info_list[0]);
      } catch (error: any) {
        // 忽略被取消的请求错误
        if (error.name === 'AbortError') {
          return;
        }

        const errorMessage = error.message || "获取会议详情时发生错误";
        setError(errorMessage);
        toast.error("获取会议详情失败", errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    if (meetingId) {
      fetchMeetingDetail(meetingId);
    }

    // 清理函数：组件卸载时取消请求
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [meetingId]);

  // 使用 useMemo 缓存转换后的表单数据，避免无限循环
  const formData = useMemo(() => {
    console.log("会议详情", meetingData);
    if (!meetingData) {
      // 返回基本的默认值，确保表单字段不为 undefined
      return {
        title: "",
        date: new Date().toISOString().split('T')[0],
        startTime: "09:00",
        endTime: "10:00",
        location: "",
        timezone: "Asia/Shanghai",
        password: "",
        hostKey: "",
        guests: [{ area: "86", phone_number: "", guest_name: "" }],
      };
    }
    return convertToFormData(meetingData);
  }, [meetingData]);

  return {
    meetingData,
    isLoading,
    error,
    formData,
  };
}
