"use client";

import { useCallback, useEffect, useRef } from "react";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { UploadCloudIcon, FileUpIcon, XIcon } from "lucide-react";
import { SUPPORTED_FILE_TYPES } from "../utils/fileParser";
import { getFileTypeInfo } from "../utils/fileTypeUtils";

interface FileUploadAreaProps {
  isDragging: boolean;
  onDragOver: (e: React.DragEvent<HTMLDivElement>) => void;
  onDragLeave: (e: React.DragEvent<HTMLDivElement>) => void;
  onDrop: (e: React.DragEvent<HTMLDivElement>) => void;
  onFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  files?: File[];
  onRemoveFile?: (index: number) => void;
  onClearFiles?: () => void;
  hasApiResult?: boolean;
}

/**
 * 文件上传区域组件
 */
export function FileUploadArea({
  isDragging,
  onDragOver,
  onDragLeave,
  onDrop,
  onFileChange,
  disabled = false,
  files = [],
  onRemoveFile,
  onClearFiles,
  hasApiResult = false,
}: FileUploadAreaProps) {
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // 当文件列表更新时，自动滚动到底部显示最新文件
  useEffect(() => {
    if (scrollContainerRef.current && files.length > 0) {
      scrollContainerRef.current.scrollTop = scrollContainerRef.current.scrollHeight;
    }
  }, [files.length]);

  return (
    <div
      className={`rounded-lg border-2 border-dashed transition-colors mb-4 p-4 h-full flex flex-col
                 border-muted-foreground/20 ${
                   isDragging
                     ? "border-primary bg-primary/5"
                     : "hover:border-primary/50"
                 } ${disabled ? "opacity-50 pointer-events-none" : ""}`}
      onDragOver={onDragOver}
      onDragLeave={onDragLeave}
      onDrop={onDrop}
    >
      {/* 如果有文件，显示文件列表 */}
      {files.length > 0 ? (
        <div className="flex flex-col h-full space-y-3">
          {/* 文件列表头部 - 包含文件数量和清除按钮 */}
          <div className="flex items-center justify-between flex-shrink-0">
            <div className="text-sm text-muted-foreground">
              已选择 {files.length} 个文件
            </div>
            {onClearFiles && (
              <Button
                variant="outline"
                size="sm"
                onClick={onClearFiles}
                disabled={disabled}
                className="h-7 px-2 text-xs"
              >
                清空
              </Button>
            )}
          </div>

          {/* 文件列表 - 可滚动区域 */}
          <div
            ref={scrollContainerRef}
            className="flex-1 overflow-y-auto space-y-2 min-h-0"
          >
            {files.map((file, index) => (
              <div
                key={`${file.name}-${index}`}
                className="rounded-md bg-muted/50 p-3 border border-border/30 hover:border-border/50 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 min-w-0 flex-1">
                    {getFileTypeInfo(file.name).icon}
                    <div className="min-w-0 flex-1">
                      <div className="font-medium truncate text-sm">
                        {file.name}
                      </div>
                      <div className="text-muted-foreground text-xs">
                        {getFileTypeInfo(file.name).text} • {(file.size / 1024 / 1024).toFixed(2)} MB
                      </div>
                    </div>
                  </div>
                  {/* 删除文件按钮 */}
                  {onRemoveFile && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 flex-shrink-0"
                      onClick={() => onRemoveFile(index)}
                      disabled={disabled}
                    >
                      <XIcon className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* 继续添加文件的提示和按钮 */}
          <div className="text-center pt-2 border-t border-dashed border-muted-foreground/20 flex-shrink-0">
            <div className="flex items-center justify-center gap-2 mb-2">
              <UploadCloudIcon className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">拖拽此处上传文件</span>
            </div>
            <Label
              htmlFor="meeting-file-upload"
              className={`inline-flex cursor-pointer items-center gap-1 rounded-md bg-primary/10 border border-primary/20 font-medium text-primary px-3 py-1.5 text-sm hover:bg-primary/20 transition-colors ${
                disabled ? "pointer-events-none opacity-50" : ""
              }`}
            >
              <FileUpIcon className="h-3 w-3" />
              <span>{hasApiResult ? "继续上传文件" : "上传文件"}</span>
            </Label>
          </div>
        </div>
      ) : (
        /* 空状态 - 显示上传提示 */
        <div className="text-center">
          <UploadCloudIcon className="mx-auto text-muted-foreground mb-4 h-12 w-12" />
          <h3 className="mb-1 font-medium text-lg">
            拖拽文件到此处
          </h3>
          <p className="text-muted-foreground mb-4 text-sm">
            上传会议相关文档，系统将自动解析文件内容并发送到后端处理
          </p>
          <p className="text-muted-foreground mb-4 text-sm">
            或点击选择文件（支持TXT、Word、Excel格式，可选择多个文件）
          </p>
          <div className="text-center">
            <Label
              htmlFor="meeting-file-upload"
              className={`inline-flex cursor-pointer items-center gap-1 rounded-md bg-primary font-medium text-primary-foreground px-4 py-2 text-sm ${
                disabled ? "pointer-events-none opacity-50" : ""
              }`}
            >
              <FileUpIcon className="h-4 w-4" />
              <span>{hasApiResult ? "继续上传文件" : "选择文件"}</span>
            </Label>
          </div>
        </div>
      )}

      {/* 隐藏的文件输入 */}
      <Input
        id="meeting-file-upload"
        type="file"
        accept={SUPPORTED_FILE_TYPES.join(',')}
        onChange={onFileChange}
        className="hidden"
        multiple
        disabled={disabled}
      />
    </div>
  );
}
