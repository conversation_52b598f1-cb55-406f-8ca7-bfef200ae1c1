import { operatorId, meetingApiClient } from "./config";

// 定义类型
export interface Department {
  department_id: string;
  department_name: string;
  department_full_name: string;
  is_main_department: boolean;
}

export interface DepartmentListResponse {
  total_count: number;
  total_page: number;
  current_page: number;
  current_size: number;
  department_list: Department[];
}

export interface User {
  userid: string;
  uuid: string;
  username: string;
  area: string;
  update_time: string;
  avatar_url: string;
  phone: string;
  email: string;
  status: string;
  job_title: string;
  staff_id: string;
  entry_time: string;
  role_name: string;
  role_code: string;
  department_list: Department[];
  user_account_type: number;
  account_version: number;
  enable_ai_account: boolean;
  ai_account_type: number;
  phone_status: number;
  add_on_largemeeting: number;
  add_on_webinar: number;
  email_status: number;
  is_voov: number;
}

export interface UserListResponse {
  has_remaining: boolean;
  next_pos: string;
  users: User[];
}

// 用户账号类型
enum UserAccountType {
  PREMIUM = 1,           // 高级账号（企业版/教育版）
  FREE = 2,             // 免费账号（企业版/教育版）
  FREE_100 = 3,         // 免费账号100方（商业版）
  PREMIUM_300 = 4,      // 高级账号300方（商业版）
  PREMIUM_500 = 5,      // 高级账号500方（商业版）
  PREMIUM_1000 = 6,     // 高级账号1000方（商业版）
  PREMIUM_2000 = 7,     // 高级账号2000方（商业版）
  PREMIUM_100 = 8,      // 高级账号100方（商业版）
  PREMIUM_ALL = 9       // 高级账号（企业版/教育版/商业版）
}

// 房间规模升级许可
enum LargeMeetingType {
  NONE = 0,             // 回收房间规模升级许可
  UPGRADE_500 = 1,      // 500方房间规模升级许可
  UPGRADE_1000 = 2,     // 1000方房间规模升级许可
  UPGRADE_2000 = 3      // 2000方房间规模升级许可
}

// Webinar观众规模升级许可
enum WebinarType {
  NONE = 0,             // 回收Webinar观众规模升级许可
  UPGRADE_300 = 1,      // Webinar观众规模提升至300观众
  UPGRADE_500 = 2,      // Webinar观众规模提升至500观众
  UPGRADE_1000 = 3,     // Webinar观众规模提升至1000观众
  UPGRADE_2000 = 4,     // Webinar观众规模提升至2000观众
  UPGRADE_3000 = 5,     // Webinar观众规模提升至3000观众
  UPGRADE_5000 = 6,     // Webinar观众规模提升至5000观众
  UPGRADE_8000 = 7,     // Webinar观众规模提升至8000观众
  UPGRADE_10000 = 8     // Webinar观众规模提升至10000观众
}

export interface CreateUserParams {
  // 必填参数
  username: string;           // 用户昵称
  userid: string;            // 调用方用于标示用户的唯一ID

  // 手机号和邮箱二选一
  phone?: string;            // 手机号码
  email?: string;            // 邮箱地址

  // 可选参数
  area?: string;             // 地区编码，默认86
  staff_id?: string;         // 员工工号
  job_title?: string;        // 员工职位
  entry_time?: number;       // 入职时间
  department_list?: string[]; // 员工部门ID
  auto_invite?: boolean;     // 自动发送邀请，默认true
  user_account_type?: UserAccountType;  // 账号类型
  add_on_largemeeting?: LargeMeetingType;  // 房间规模升级许可
  add_on_webinar?: WebinarType;  // Webinar观众规模升级许可
}

// 创建部门
export const createDepartment = {
  method: "POST",
  path: "/meetings/departments",
  handler: async (departmentName: string) => {
    try {
      const response = await meetingApiClient.post("/v1/departments", {
        userid: operatorId,
        department_name: departmentName
      });

      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      console.error("Error creating department:", error);
      throw error;
    }
  },
};

// 获取部门列表
export const getDepartments = {
  method: "GET",
  path: "/meetings/departments",
  handler: async () => {
    try {
      const response = await meetingApiClient.get("/v1/departments", {
        params: {
          userid: operatorId
        }
      });

      return response.data as DepartmentListResponse;
    } catch (error: any) {
      console.error("Error fetching departments:", error);
      throw error;
    }
  },
};

// 获取用户列表
// 获取用户信息
// 根据用户ID查询用户是否存在及其激活状态
export const getUser = {
	method: "GET",
	path: "/meetings/users",
	handler: async (userId: string) => {
		try {
			// 调用腾讯会议API获取用户信息
			const response = await meetingApiClient.get(`/v1/users/${userId}`, {
				params: {
					operator_id: operatorId,
					operator_id_type: "1",
				},
			});

			// 解析用户数据
			const user = response.data;
			// 检查用户状态，status为1表示已激活
			if (user && user.status === "1") {
				return { exists: true, activated: true, account_type: user.user_account_type };
			}
			// 用户存在但未激活
			return { exists: true, activated: false };
		} catch (error: any) {
			// 如果是404错误，说明用户不存在
			if (error.response.data.error_info.message === "用户不存在") {
				return { exists: false, activated: false };
			}
			// 记录其他错误并抛出
			console.error("Error fetching user:", error);
			throw error;
		}
	},
};

// 创建用户
export const createUser = {
  method: "POST",
  path: "/meetings/create/users",
  handler: async (params: CreateUserParams) => {
    try {
      console.log('创建用户参数:', params);
      
      // 确保必填参数存在
      if (!params.username || !params.userid) {
        throw new Error('缺少必填参数: username, userid');
      }

      const requestBody = {
        ...params,
        operator_id: operatorId,
        operator_id_type: "1",
        auto_invite: params.auto_invite ?? true,  // 默认开启自动邀请
        area: params.area ?? "86"                 // 默认地区编码
      };

      // console.log('发送到腾讯会议的请求体:', requestBody);

      const response = await meetingApiClient.post("/v1/users", requestBody);

      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      console.error("Error creating user:", error);
      throw error;
    }
  },
};

// 更新用户
export const updateUser = {
  method: "PUT",
  path: "/meetings/users/:userid",
  handler: async (userid: string, departmentList: string[]) => {
    try {
      const response = await meetingApiClient.put(`/v1/users/${userid}`, {
        userid,
        department_list: departmentList,
        operator_id: operatorId,
        operator_id_type: "1"
      });

      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      console.error("Error updating user:", error);
      throw error;
    }
  },
};

// 删除用户
export const deleteUser = {
	// HTTP 方法
	method: "DELETE",
	// API 路径
	path: "/meetings/users/:userid",
	// 处理函数
	handler: async (userid: string) => {
		try {
			// 调用腾讯会议 API 删除用户
			const response = await meetingApiClient.delete(
				`/v1/users/${userid}`,
				{
					// URL 查询参数
					params: {
						operator_id: operatorId,
						operator_id_type: "1",
					},
					// 请求体数据
					data: {
						userid,
						operator_id: operatorId,
						operator_id_type: "1",
					},
				},
			);
			// 返回成功响应
			return { success: true, data: response.data };
		} catch (error: any) {
			// 错误处理
			console.error("Error deleting user:", error);
			throw error;
		}
	},
};
