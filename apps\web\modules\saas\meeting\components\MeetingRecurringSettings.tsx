"use client";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@ui/components/select";
import type { Control, UseFormWatch } from "react-hook-form";
import type { MeetingFormValues } from "../utils/meetingFormValidation";

interface MeetingRecurringSettingsProps {
  control: Control<MeetingFormValues>;
  watch: UseFormWatch<MeetingFormValues>;
}

/**
 * 周期性会议设置组件
 * 包含周期性会议的所有相关设置
 */
export function MeetingRecurringSettings({ control, watch }: MeetingRecurringSettingsProps) {
  const isRecurring = watch("isRecurring");
  const untilType = watch("untilType");

  return (
    <div className="space-y-6">
      {/* 会议类型 - 改为勾选框 */}
      <FormField
        control={control}
        name="isRecurring"
        render={({ field }) => (
          <FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
            <FormControl>
              <input
                type="checkbox"
                checked={field.value}
                onChange={field.onChange}
                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
              />
            </FormControl>
            <div className="space-y-1 leading-none">
              <FormLabel className="text-sm font-medium">
                周期性会议
              </FormLabel>
              <FormDescription className="text-xs">
                勾选此项将创建周期性会议，可以设置重复规则
              </FormDescription>
            </div>
          </FormItem>
        )}
      />

      {/* 周期性会议设置 - 仅在勾选了周期性会议时显示 */}
      {isRecurring && (
        <div className="space-y-4">
          <div>
            {/* 重复类型 */}
            <FormField
              control={control}
              name="recurringType"
              render={({ field }) => (
                <FormItem className="max-w-[400px]">
                  <FormLabel className="text-sm font-medium">
                    重复频率
                  </FormLabel>
                  <Select
                    onValueChange={(value) =>
                      field.onChange(
                        Number(value),
                      )
                    }
                    value={field.value?.toString() ?? "0"}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择重复类型" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="0">
                        <p className="text-xs">
                          每天
                        </p>
                      </SelectItem>
                      <SelectItem value="1">
                        <p className="text-xs">
                          每个工作日
                        </p>
                      </SelectItem>
                      <SelectItem value="2">
                        <p className="text-xs">
                          每周（周二）
                        </p>
                      </SelectItem>
                      <SelectItem value="3">
                        <p className="text-xs">
                          每两周（周二）
                        </p>
                      </SelectItem>
                      <SelectItem value="4">
                        <p className="text-xs">
                          每月（27日）
                        </p>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 结束类型 */}
            <FormField
              control={control}
              name="untilType"
              render={({ field }) => (
                <FormItem className="max-w-[400px]">
                  <FormLabel className="text-sm font-medium">
                    结束重复
                  </FormLabel>
                  <Select
                    onValueChange={(value) =>
                      field.onChange(
                        Number(value),
                      )
                    }
                    value={field.value?.toString() ?? "0"}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder="选择结束类型"
                          className="text-xs"
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="0">
                        <p className="text-xs">
                          结束于某天
                        </p>
                      </SelectItem>
                      <SelectItem value="1">
                        <p className="text-xs">
                          限定会议次数
                        </p>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 结束日期 - 仅在选择了按日期结束时显示 */}
            {untilType === 0 && (
              <FormField
                control={control}
                name="untilDate"
                render={({ field }) => (
                  <FormItem className="max-w-[400px]">
                    <FormLabel className="text-sm font-medium">
                      结束日期
                    </FormLabel>
                    <FormControl>
                      <Input
                        className="text-xs"
                        type="date"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription className="text-xs">
                      结束日期与第一场会议的开始时间换算成的场次数不能超过限制
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* 会议次数 - 仅在选择了按次数结束时显示 */}
            {untilType === 1 && (
              <FormField
                control={control}
                name="untilCount"
                render={({ field }) => (
                  <FormItem className="max-w-[400px]">
                    <FormLabel className="text-sm font-medium">
                      会议次数
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min={1}
                        max={200}
                        className="text-xs"
                        {...field}
                        onChange={(e) =>
                          field.onChange(
                            Number(
                              e.target
                                .value,
                            ),
                          )
                        }
                      />
                    </FormControl>
                    <FormDescription className="text-xs">
                      每天、每个工作日、每周最大支持200场子会议；每两周、每月最大支持50场子会议
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
          </div>
        </div>
      )}
    </div>
  );
}
