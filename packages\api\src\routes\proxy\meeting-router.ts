import { Hono } from "hono";
import { createSimpleN8nMeetingProxy } from "../../middleware/n8n-meeting-proxy";
import { config } from "@repo/config";

/**
 * n8n会议代理路由器
 * 处理 /api/n8n_meeting_proxy/* 路径的所有请求
 */
const router = new Hono();

// 获取n8n服务器配置
const getN8nMeetingBaseUrl = () => {
  // 优先使用环境变量中的配置，然后使用config中的配置
  const baseUrl = process.env.N8N_MEETING_BASE_URL || config.n8nMeeting?.baseUrl || "http://localhost:5678/webhook/v1";

  // logger.info("N8N Proxy Configuration", { baseUrl });

  return baseUrl;
};

// 获取超时配置
const getMeetingTimeout = () => {
  const timeout = process.env.N8N_MEETING_TIMEOUT ? Number.parseInt(process.env.N8N_MEETING_TIMEOUT) : (config.n8nMeeting?.timeout || 30000);
  return timeout;
};

/**
 * 注册n8n代理中间件处理所有路径
 * 支持所有HTTP方法：GET, POST, PUT, DELETE, PATCH, OPTIONS等
 */
router.all("/*", createSimpleN8nMeetingProxy(getN8nMeetingBaseUrl(), getMeetingTimeout()));

// 添加健康检查端点
router.get("/health", (c) => {
  return c.json({
    code: 200,
    message: "N8N代理服务正常",
    data: {
      baseUrl: getN8nMeetingBaseUrl(),
      timeout: getMeetingTimeout(),
      timestamp: new Date().toISOString()
    }
  });
});

export const proxyMeetingRouter = router;
