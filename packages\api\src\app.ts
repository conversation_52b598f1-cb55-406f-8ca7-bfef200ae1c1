import { auth } from "@repo/auth";
import { getBaseUrl } from "@repo/utils";
import { apiReference } from "@scalar/hono-api-reference";
import { Hono } from "hono";
import { openAPISpecs } from "hono-openapi";
import {} from "openapi-merge";
import {} from "openapi-merge";
import { mergeOpenApiSchemas } from "./lib/openapi-schema";
import { corsMiddleware } from "./middleware/cors";
import { loggerMiddleware } from "./middleware/logger";
import { adminRouter } from "./routes/admin/router";
import { aiRouter } from "./routes/ai";
import { authRouter } from "./routes/auth";
import { contactRouter } from "./routes/contact/router";
import { healthRouter } from "./routes/health";
import { meetingApiRouter } from "./routes/meeting/router";
import { newsletterRouter } from "./routes/newsletter";
import { organizationsRouter } from "./routes/organizations/router";
import { paymentsRouter } from "./routes/payments/router";
import { uploadsRouter } from "./routes/uploads";
import { webhooksRouter } from "./routes/webhooks";
import { shareholderRegistryRouter } from "./routes/shareholder-registry/router";
import { investorManagementRouter } from "./routes/investor-management/router";
import { timeRouter } from "./routes/time";
import { n8nProxyRouter } from "./routes/n8n_proxy/router";
import { proxyMeetingRouter } from "./routes/proxy/meeting-router";

export const app = new Hono().basePath("/api");

app.use(loggerMiddleware);
app.use(corsMiddleware);

const appRouter = app
	.route("/", authRouter) // 认证路由 - 最高优先级
	.route("/", healthRouter) // 健康检查路由
	.route("/", timeRouter) // 时间同步路由
	.route("/", webhooksRouter) // Webhook路由
	.route("/", aiRouter) // AI路由
	.route("/", uploadsRouter) // 上传路由
	.route("/", paymentsRouter) // 支付路由
	.route("/", contactRouter) // 联系路由
	.route("/", newsletterRouter) // 新闻订阅路由
	.route("/", organizationsRouter) // 组织路由
	.route("/", adminRouter) // 管理员路由
	.route("/", shareholderRegistryRouter) // 股东名册路由
	.route("/", investorManagementRouter) // 投资人管理路由
	.route("/", meetingApiRouter) // 会议API路由
	.route("/n8n_meeting_proxy", proxyMeetingRouter) // 会议专用n8n代理路由
	.route("/", n8nProxyRouter); // n8n代理路由 - 股东分析专用

app.get(
	"/app-openapi",
	openAPISpecs(app, {
		documentation: {
			info: {
				title: "StarLink API",
				version: "1.0.0",
			},
			servers: [
				{
					url: getBaseUrl(),
					description: "API server",
				},
			],
		},
	}),
);

app.get("/openapi", async (c) => {
	const authSchema = await auth.api.generateOpenAPISchema();
	const appSchema = await (
		app.request("/api/app-openapi") as Promise<Response>
	).then((res) => res.json());

	const mergedSchema = mergeOpenApiSchemas({
		appSchema,
		authSchema: authSchema as any,
	});

	return c.json(mergedSchema);
});

app.get(
	"/docs",
	apiReference({
		theme: "saturn",
		spec: {
			url: "/api/openapi",
		},
	}),
);

export type AppRouter = typeof appRouter;
