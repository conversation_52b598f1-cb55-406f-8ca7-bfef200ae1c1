"use client";

import { useState, useCallback } from "react";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { UploadCloudIcon, FileSpreadsheetIcon, X } from "lucide-react";
import { toast } from "sonner"; 
import * as XLSX from "xlsx";

interface ExcelFileUploadProps {
  onImport: (file: File) => void;
  onCancel: () => void;
  isLoading?: boolean;
  disabled?: boolean;
}

/**
 * Excel文件上传组件
 * 专门用于上传Excel文件(.xlsx, .xls)
 * 支持拖拽上传和文件选择
 */
export function ExcelFileUpload({
  onImport,
  onCancel,
  isLoading = false,
  disabled = false,
}: ExcelFileUploadProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // 检查文件类型是否为Excel
  const isExcelFile = useCallback((file: File): boolean => {
    const validTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
    ];
    const validExtensions = ['.xlsx', '.xls'];
    const fileName = file.name.toLowerCase();
    
    return validTypes.includes(file.type) || 
           validExtensions.some(ext => fileName.endsWith(ext));
  }, []);

  // 验证文件
  const validateFile = useCallback((file: File): boolean => {
    if (!isExcelFile(file)) {
      toast.error("请选择Excel文件（.xlsx 或 .xls 格式）");
      return false;
    }

    // 文件大小限制 10MB
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      toast.error("文件大小不能超过10MB");
      return false;
    }

    return true;
  }, [isExcelFile]);

  // 处理文件选择
  const handleFileSelect = useCallback((file: File) => {
    if (validateFile(file)) {
      setSelectedFile(file);
    }
  }, [validateFile]);

  // 处理拖拽事件
  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  // 处理文件输入变化
  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  // 确认导入
  const handleConfirmImport = useCallback(() => {
    if (selectedFile) {
      onImport(selectedFile);
    }
  }, [selectedFile, onImport]);

  // 移除选中的文件
  const handleRemoveFile = useCallback(() => {
    setSelectedFile(null);
  }, []);

  return (
    <div className="space-y-4">
      {/* 创建一个excel模板，提供按钮下载 */}
      <div className="flex justify-end">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => {
            // 创建一个excel文件，里面有三列，国家代码，手机号，成员名称
            // 使用xlsx库创建
            // 创建一个excel文件，里面有三列，国家代码，手机号，成员名称
            // 使用xlsx库创建
            // 下载这个文件
            const workbook = XLSX.utils.book_new();
            const worksheet = XLSX.utils.aoa_to_sheet([
              ["国家/地区代码（如86）", "电话", "成员名称"],
            ]);
            XLSX.utils.book_append_sheet(workbook, worksheet, "成员列表");
            const excelBuffer = XLSX.write(workbook, { type: "array", bookType: "xlsx" });
            const blob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });
            const url = URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = "成员导入模板.xlsx";
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url); 
          }}
        >
          下载Excel模板
        </Button>
      </div>

      {/* 文件上传区域 */}
      {!selectedFile && (
        <div
          className={`rounded-lg border-2 border-dashed text-center transition-colors p-6
                     border-muted-foreground/20 ${
                       isDragging 
                         ? "border-primary bg-primary/5" 
                         : "hover:border-primary/50"
                     } ${disabled ? "opacity-50 pointer-events-none" : ""}`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <UploadCloudIcon className="mx-auto text-muted-foreground mb-4 h-12 w-12" />
          <h3 className="mb-1 font-medium text-lg">
            拖拽Excel文件到此处
          </h3>
          <p className="text-muted-foreground mb-4 text-sm">
            支持 .xlsx 和 .xls 格式，文件大小不超过10MB
          </p>
          <p className="text-muted-foreground mb-4 text-sm">
            Excel文件应包含3列：国家代码、手机号、成员名称
          </p>
          <div className="text-center">
            <Label
              htmlFor="excel-file-upload"
              className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 cursor-pointer"
            >
              选择Excel文件
            </Label>
            <Input
              id="excel-file-upload"
              type="file"
              accept=".xlsx,.xls"
              onChange={handleFileChange}
              className="hidden"
              disabled={disabled}
            />
          </div>
        </div>
      )}

      {/* 选中的文件显示 */}
      {selectedFile && (
        <div className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <FileSpreadsheetIcon className="h-8 w-8 text-green-600" />
              <div>
                <p className="font-medium text-sm">{selectedFile.name}</p>
                <p className="text-xs text-gray-500">
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </p>
              </div>
            </div>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleRemoveFile}
              disabled={disabled || isLoading}
              className="text-red-500 hover:text-red-700"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="flex justify-end space-x-2">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          取消
        </Button>
        <Button
          type="button"
          onClick={handleConfirmImport}
          disabled={!selectedFile || isLoading}
          loading={isLoading}
        >
          {isLoading ? "导入中..." : "确认导入"}
        </Button>
      </div>
    </div>
  );
}
