/**
 * 收藏/星标管理Hook集合
 *
 * @fileoverview 提供投资者收藏功能的Hook，包括创建、删除和切换收藏状态
 * <AUTHOR>
 * @since 2025-07-22
 * @version 1.0.0
 *
 * 功能特性：
 * - 投资者标签创建（收藏功能）
 * - 投资者标签删除（取消收藏）
 * - 收藏状态切换
 * - 自动缓存刷新
 * - React Query集成
 * - 错误处理和状态管理
 */

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createInvestorTag, deleteInvestorTag, toggleFavoriteStatus } from "../lib/Star";
import type { CreateInvestorTagRequest, DeleteInvestorTagRequest } from "../lib/Star";
import type { UseMutationOptions } from "@tanstack/react-query";

/**
 * 创建投资人标签Hook（收藏功能）
 *
 * @param options - React Query mutation配置选项
 * @returns mutation对象，包含mutate、mutateAsync等方法
 *
 */
export function useCreateInvestorTag(options?: UseMutationOptions<any, Error, CreateInvestorTagRequest>) {
	const queryClient = useQueryClient();

	return useMutation<any, Error, CreateInvestorTagRequest>({
		mutationFn: createInvestorTag,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["sequenceData"] });
		},
		...options,
	});
}

/**
 * 删除投资人标签Hook（取消收藏）
 *
 * @param options - React Query mutation配置选项
 * @returns mutation对象，包含mutate、mutateAsync等方法
 *
 */
export function useDeleteInvestorTag(options?: UseMutationOptions<any, Error, DeleteInvestorTagRequest>) {
	const queryClient = useQueryClient();

	return useMutation<any, Error, DeleteInvestorTagRequest>({
		mutationFn: deleteInvestorTag,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["sequenceData"] });
		},
		...options,
	});
}

/**
 * 切换收藏状态的参数接口
 */
export interface ToggleFavoriteParams {
	/** 组织ID */
	organizationId: string;
	/** 公司过滤器ID */
	companyFilterId: string;
	/** 投资者代码 */
	investorCode: string;
	/** 当前是否已收藏 */
	isFavorited: boolean;
}

/**
 * 切换收藏状态Hook（收藏/取消收藏）
 *
 * 根据当前收藏状态自动选择创建或删除操作
 *
 * @param options - React Query mutation配置选项
 * @returns mutation对象，包含mutate、mutateAsync等方法
 *
 * 注意：不再自动刷新缓存，由调用组件控制刷新逻辑以避免数据竞争
 *
 */
export function useToggleFavorite(options?: UseMutationOptions<any, Error, ToggleFavoriteParams>) {
	return useMutation<any, Error, ToggleFavoriteParams>({
		mutationFn: async (params: ToggleFavoriteParams) => {
			return await toggleFavoriteStatus(
				params.organizationId,
				params.companyFilterId,
				params.investorCode,
				params.isFavorited
			);
		},
		...options,
	});
}

