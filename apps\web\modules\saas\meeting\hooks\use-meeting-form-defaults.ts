import { useMemo } from "react";
import type { MeetingFormValues } from "../utils/meetingFormValidation";

/**
 * 计算下一个30分钟刻度的时间
 */
function getNextHalfHourTime(): string {
  const now = new Date();
  const currentMinutes = now.getMinutes();
  const currentHour = now.getHours();
  
  // 计算下一个30分钟刻度（必须晚于当前时间）
  let nextMinutes: number;
  let nextHour = currentHour;
  
  if (currentMinutes < 30) {
    nextMinutes = 30;
  } else {
    nextMinutes = 0;
    nextHour = (currentHour + 1) % 24;
  }
  
  return `${nextHour.toString().padStart(2, '0')}:${nextMinutes.toString().padStart(2, '0')}`;
}

/**
 * 计算结束时间（开始时间 + 1小时）
 */
function getEndTime(startTime: string): string {
  const [startHour, startMinute] = startTime.split(':').map(Number);
  const endHour = (startHour + 1) % 24;
  const endMinutes = startMinute;
  
  return `${endHour.toString().padStart(2, '0')}:${endMinutes.toString().padStart(2, '0')}`;
}

/**
 * 获取默认的结束日期（7天后）
 */
function getDefaultUntilDate(): string {
  const date = new Date();
  date.setDate(date.getDate() + 7);
  return date.toISOString().split('T')[0];
}

/**
 * 会议表单默认值 Hook
 */
export function useMeetingFormDefaults(): Partial<MeetingFormValues> {
  return useMemo(() => {
    const startTime = getNextHalfHourTime();
    const endTime = getEndTime(startTime);
    
    return {
      title: "",
      date: new Date().toISOString().split('T')[0],
      startTime,
      endTime,
      location: "",
      isRecurring: false,
      timezone: "Asia/Shanghai",
      password: Math.floor(100000 + Math.random() * 900000).toString(),//生成随机六位数字密码
      requirePassword: true,
      muteParticipantsOnEntry: "muteOn" as const,
      waitingRoom: false,
      recordMeeting: true,
      multiPlatform: true,
      enterInAdvance: true,
      allowChat: true,
      allowScreenShare: true,
      allowParticipantsToRename: true,
      enableHostKey: true,
      hostKey: Math.floor(100000 + Math.random() * 900000).toString(),//生成随机六位数字密码
      enableDocUpload: true,
      screenWatermark: false,
      disableScreenshot: false,
      autoTranscribe: true,
      playIvrOnJoin: false,
      playIvrOnLeave: false,
      onlyEnterpriseUserAllowed: false,
      recurringType: 0,
      untilType: 0,
      untilDate: getDefaultUntilDate(),
      untilCount: 7,
      guests: [{ area: "86", phone_number: "", guest_name: "" }],
      enable_enroll: false,
      approval_mode: "manual" as const,
    };
  }, []);
}
