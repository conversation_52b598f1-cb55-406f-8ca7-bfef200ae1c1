/**
 * 投资者对话框组件
 *
 * @fileoverview 投资者详情弹窗组件，提供基金资料展示和联系人管理功能
 * <AUTHOR>
 * @since 2025-07-22
 * @version 1.0.0
 *
 * 功能特性：
 * - 投资者基金详情展示
 * - 双标签页设计（资料/联系人）
 * - HTML内容安全渲染（Shadow DOM）
 * - 收藏功能集成
 * - 联系人信息管理
 * - 刷新功能和动画效果
 * - 响应式设计
 * - 错误处理和加载状态
 */

import React, { useState, useEffect, useRef } from "react";
import { LargeDialog, LargeDialogContent, LargeDialogTitle, LargeDialogBody, LargeDialogEmptyState, LargeDialogErrorState } from "@ui/components/large-dialog";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@ui/components/tabs";
import { Button } from "@ui/components/button";
import { ShadowHtmlRenderer } from "@ui/components/shadow-html-renderer";
import { Skeleton } from "@ui/components/skeleton";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import type { FundCard } from "../lib/html_dialog";
import { useFundDetail } from "../hooks/useFundDetail"; // 路径按实际调整
import { DialogDescription } from "@ui/components/dialog";
import { ContactsDialog, type ContactsDialogRef } from "@saas/market/components/ContactsDialog";
import { Star, RefreshCcw } from "lucide-react";



/**
 * 投资者对话框组件属性接口
 *
 * 定义投资者详情弹窗组件的所有属性和回调函数
 */
export interface InvestorDialogProps {
  /** 对话框是否打开 */
  open: boolean;
  /** 对话框开关状态变化回调 */
  onOpenChange: (open: boolean) => void;
  /** 当前选中的基金卡片数据 */
  selectedCard: FundCard | null;
  /** 基金详情HTML内容（已废弃，使用useFundDetail Hook获取） */
  html?: string;
  /** 是否正在加载（已废弃，使用useFundDetail Hook管理） */
  loading: boolean;
  /** 错误信息（已废弃，使用useFundDetail Hook管理） */
  error: string;
  /** 是否已收藏 */
  isFavorite: boolean;
  /** 切换收藏状态的回调函数 */
  onToggleFavorite: () => void;
  /** 子组件内容 */
  children?: React.ReactNode;
  /** 刷新数据的回调函数 */
  onRefresh?: () => void;
  /** 组织ID */
  organizationId?: string;
  /** 公司过滤器ID */
  companyFilterId?: string;
  /** 投资者代码 */
  investorCode?: string;
  /** 标签ID */
  tagId?: string;
}

/**
 * 投资者对话框组件
 *
 * 提供投资者详情信息的弹窗展示，包含基金资料和联系人信息两个标签页
 *
 * @param props - 组件属性对象
 * @returns 投资者对话框组件
 *
 * 功能特性：
 * - 双标签页设计（资料/联系人）
 * - 基金详情数据获取和展示
 * - 收藏功能集成
 * - 刷新功能和动画效果
 * - 联系人信息管理
 * - 响应式设计
 *
 * @example
 * ```tsx
 * <InvestorDialog
 *   open={isOpen}
 *   onOpenChange={setIsOpen}
 *   selectedCard={selectedFundCard}
 *   isFavorite={isFavorited}
 *   onToggleFavorite={handleToggleFavorite}
 * />
 * ```
 */
export function InvestorDialog({
  open,
  onOpenChange,
  selectedCard,
  isFavorite,
  onToggleFavorite,
  children,
}: InvestorDialogProps) {
  /** 当前激活的标签页，默认为"info"（资料） */
  const [tab, setTab] = useState("info");

  /** 获取基金详情数据的Hook */
  const { data, isLoading, error: fetchError, refetch } = useFundDetail(selectedCard);

  /** 刷新按钮动画状态管理 */
  const [isRefreshAnimating, setIsRefreshAnimating] = useState(false);

  /** ContactsDialog组件的引用，用于调用其刷新方法 */
  const contactsDialogRef = useRef<ContactsDialogRef>(null);

  /**
   * 当选中的卡片变化时，重置标签页为默认的"info"
   */
  useEffect(() => {
    if (selectedCard) {
      setTab("info");
    }
  }, [selectedCard?.code]);

  // data?.html 就是HTML内容
		// isLoading 控制骨架屏
		// error?.error 控制错误提示

		// 处理刷新按钮点击，添加旋转动画效果
		const handleRefresh = () => {
			// 设置动画状态为true
			setIsRefreshAnimating(true);

			// 根据当前选中的标签页刷新不同的内容
			switch (tab) {
				case "info":
					// 资料标签页：刷新基金详情
					refetch();
					break;
				case "contacts":
					// 联系人标签页：调用 ContactsDialog 的刷新方法
					contactsDialogRef.current?.refetch();
					break;
				case "shares":
					// 持股标签页：刷新持股数据
					console.log("刷新持股数据");
					break;
				case "meetings":
					// 会议标签页：刷新会议数据
					console.log("刷新会议数据");
					break;
				default:
					// 默认情况下刷新基金详情
					refetch();
			}

			// 1秒后重置动画状态
			setTimeout(() => {
				setIsRefreshAnimating(false);
			}, 1000);
		};

		return (
			<>
				<LargeDialog open={open} onOpenChange={onOpenChange}>
					<LargeDialogContent>
						{/*不加这个Description会警告*/}
						<DialogDescription className="sr-only">
							弹窗展示投资人资料详情，包含基本信息、联系人、持股、会议等。
						</DialogDescription>
						<LargeDialogTitle className="flex items-center gap-1 text-3xl mb-0 mt-5 text-black dark:text-slate-200 px-4">
							<span>
								{selectedCard?.name ?? "基金详情"}
								{selectedCard?.code && (
									<span className="text-black ml-2">({selectedCard.code})</span>
								)}
							</span>
							<Button
								variant="ghost"
								className="flex items-center gap-1 h-11 px-2 hover:bg-transparent ml-1 rounded-md"
								title="公司收藏"
								onClick={onToggleFavorite}
							>
								<Star
									className={cn(
										"w-7 h-7 cursor-pointer",
										isFavorite
											? "fill-yellow-500 text-yellow-500 dark:fill-yellow-400 dark:text-yellow-400"
											: "fill-none text-yellow-500 dark:text-stone-500",
									)}
								/>
							</Button>
						</LargeDialogTitle>
						<LargeDialogBody>
							{children ? (
								children
							) : (
								<Tabs
									defaultValue="info"
									value={tab}
									onValueChange={setTab}
									className="w-full flex flex-col h-full border-0"
								>
									<TabsList className="relative flex w-auto max-w-full bg-transparent dark:bg-transparent p-0 gap-8 mx-4 border-0 shadow-none justify-start mb-2">
										<TabsTrigger
											value="info"
											className="px-1 py-2 text-sm font-medium transition-all cursor-pointer"
										>
											资料
										</TabsTrigger>
										<TabsTrigger
											value="contacts"
											className="px-1 py-2 text-sm font-medium transition-all cursor-pointer"
										>
											联系人
										</TabsTrigger>
										<TabsTrigger
											value="shares"
											className="px-1 py-2 text-sm font-medium transition-all cursor-pointer"
										>
											持股
										</TabsTrigger>
										<TabsTrigger
											value="meetings"
											className="px-1 py-2 text-sm font-medium transition-all cursor-pointer"
										>
											会议
										</TabsTrigger>
										<div className="flex-1 flex justify-end items-center">
											<TooltipProvider>
												<Tooltip delayDuration={0}>
													<TooltipTrigger asChild>
														<Button
															type="button"
															onClick={
																handleRefresh
															}
															variant="outline"
															size="icon"
															className={cn(
																"h-9 w-9", // 确保高宽一致为9（36px）
																"rounded-md", // 现代化的圆角
																"bg-background text-foreground border-border hover:bg-accent", // 使用默认白色背景
																"transition-all duration-200", // 平滑过渡效果
															)}
															loading={isLoading}
															disabled={isLoading}
														>
															<RefreshCcw
																className={cn(
																	"size-4 m-0",
																	isRefreshAnimating &&
																		"animate-spin",
																)}
															/>
														</Button>
													</TooltipTrigger>
													<TooltipContent>
														<p>刷新</p>
													</TooltipContent>
												</Tooltip>
											</TooltipProvider>
										</div>
									</TabsList>
									{/* 可滚动的内容区域 */}
									<div className="flex-1 overflow-auto">
										<TabsContent
											value="info"
											className="focus-visible:outline-none mt-0"
										>
											{/* 既设置最小高度也设置最大高度，让这个区域固定高度即可 */}
											<div className="min-h-[720.9px] max-h-[720.9px] flex flex-col">
												{isLoading ? (
													<div className="w-full p-6 space-y-4">
														{/* 标题骨架 */}
														<div className="space-y-2">
															<Skeleton className="h-8 w-3/4" />
															<Skeleton className="h-4 w-1/2" />
														</div>

														{/* 内容区域骨架 */}
														<div className="space-y-6">
															{/* 第一段内容 */}
															<div className="space-y-3">
																<Skeleton className="h-4 w-full" />
																<Skeleton className="h-4 w-5/6" />
																<Skeleton className="h-4 w-4/5" />
															</div>

															{/* 第二段内容 */}
															<div className="space-y-3">
																<Skeleton className="h-4 w-full" />
																<Skeleton className="h-4 w-3/4" />
																<Skeleton className="h-4 w-5/6" />
															</div>

															{/* 表格或图表骨架 */}
															<div className="space-y-2">
																<Skeleton className="h-6 w-1/3" />
																<div className="grid grid-cols-3 gap-4">
																	<Skeleton className="h-20 w-full" />
																	<Skeleton className="h-20 w-full" />
																	<Skeleton className="h-20 w-full" />
																</div>
															</div>

															{/* 更多内容骨架 */}
															<div className="space-y-3">
																<Skeleton className="h-4 w-full" />
																<Skeleton className="h-4 w-2/3" />
																<Skeleton className="h-4 w-3/4" />
																<Skeleton className="h-4 w-1/2" />
															</div>
														</div>
													</div>
												) : fetchError ? (
													<LargeDialogErrorState
														error={
															fetchError.message ||
															"加载失败"
														}
													/>
												) : data?.error ? (
													<LargeDialogErrorState
														error={data.error}
													/>
												) : data?.html?.trim() ? (
													<div className="w-full h-full max-h-[720.9px]">
														<ShadowHtmlRenderer
															html={data.html}
															className="w-full min-h-full"
															injectTailwindStyles={
																true
															}
														/>
													</div>
												) : data?.markdown ? (
													<div className="w-full h-full p-4">
														<div className="prose prose-sm max-w-none">
															{data.markdown}
														</div>
													</div>
												) : (
													<LargeDialogEmptyState message="暂无资料" />
												)}
											</div>
										</TabsContent>
										<TabsContent
											value="contacts"
											className="focus-visible:outline-none mt-0"
										>
											<div className="min-h-[720.9px] flex flex-col">
												<div className="max-h-[720.9px] flex flex-col">
													{tab === "contacts" && (
														<ContactsDialog
															cardCode={
																selectedCard?.code
															}
															ref={
																contactsDialogRef
															}
														/>
													)}
												</div>
											</div>
										</TabsContent>
										<TabsContent
											value="shares"
											className="focus-visible:outline-none mt-0"
										>
											{/* {既设置最小高度也设置最大高度，让这个局面固定高度即可} */}
											<div className="min-h-[720.9px] max-h-[720.9px] flex flex-col">
												<div className="text-muted-foreground text-center py-8">
													持股信息开发中...
												</div>
											</div>
										</TabsContent>
										<TabsContent
											value="meetings"
											className="focus-visible:outline-none mt-0"
										>
											<div className="min-h-[720.9px] max-h-[720.9px] flex flex-col">
												<div className="text-muted-foreground text-center py-8">
													会议信息开发中...
												</div>
											</div>
										</TabsContent>
									</div>
								</Tabs>
							)}
						</LargeDialogBody>
					</LargeDialogContent>
				</LargeDialog>
			</>
		);
}
