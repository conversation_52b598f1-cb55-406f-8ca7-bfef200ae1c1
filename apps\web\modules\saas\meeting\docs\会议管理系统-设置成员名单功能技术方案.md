1. 技术架构

1.1 系统整体架构

此处为具体设计图

1.2 模块划分和组件层次结构

apps/web/modules/saas/meeting/
├── components/
│   ├── MeetingGuestManagement.tsx      # 主要成员管理组件
│   ├── GuestBatchImportDialog.tsx      # Excel批量导入对话框
│   ├── GuestSystemImportDialog.tsx     # 系统股东导入对话框
│   ├── ExcelFileUpload.tsx             # 文件上传组件
│   ├── MeetingForm.tsx                 # 会议创建表单
│   └── ChangeMeetingForm.tsx           # 会议编辑表单
├── utils/
│   ├── meetingFormValidation.ts        # 表单验证Schema
│   ├── guestValidation.ts              # 成员数据验证工具
│   └── guestExcelParser.ts             # Excel文件解析工具
├── hooks/
│   ├── use-meeting-form-submit.ts      # 会议创建提交逻辑
│   ├── use-meeting-edit-submit.ts      # 会议编辑提交逻辑
│   └── use-guest-shareholders.ts       # 股东数据获取Hook
└── types/
    └── types.ts                        # 类型定义

1.3 技术选型说明
| 前端框架 | Next.js 15.2.3 | App Router架构，SSR支持 |
| UI组件库 | Radix UI + Shadcn UI | 无障碍访问，高度可定制 |
| 表单管理 | React Hook Form 7.54.2 | 高性能表单状态管理 |
| 表单验证 | Zod 3.24.2 | TypeScript优先的Schema验证 |
| 状态管理 | Jotai 2.12.1 | 原子化状态管理 |
| 文件处理 | XLSX 0.18.5 | Excel文件解析 |
| API框架 | Hono 4.7.2 | 轻量级Web框架 |
| 数据库ORM | Prisma | 类型安全的数据库访问 |
| 样式框架 | Tailwind CSS 4.0.17 | 实用优先的CSS框架 |

1.4 数据流向图

此处为具体设计图
2. 数据库设计

2.1 成员名单相关数据表结构

由于成员名单数据主要通过第三方API传递，本地数据库主要存储股东信息用于系统导入功能：

-- 股东信息表 (用于系统导入)
model Shareholder {
  id                      String   @id @default(cuid())
  organizationId          String   // 组织ID 关联股东信息
  securitiesAccountName   String   // 证券账户名称 (用作成员姓名)
  contactNumber          String   // 联系电话 (用作成员联系方式)
  createdAt              DateTime @default(now())
  updatedAt              DateTime @updatedAt
  
  organization Organization @relation(fields: [organizationId], references: [id])
  
  @@map("shareholder")
}

-- 组织表
model organization{
  id           String        @id // 关联股东信息
  name         String
  @@map("organization")
}

2.2 字段定义和数据类型

字段名
数据类型
约束
说明
id
String
PRIMARY KEY
股东记录唯一标识
organizationId
String
NOT NULL, FOREIGN KEY
关联组织ID
securitiesAccountName
String
NOT NULL
股东姓名，导入时作为成员姓名
contactNumber
String
NOT NULL
联系电话，导入时作为成员联系方式
createdAt
DateTime
DEFAULT now()
创建时间
updatedAt
DateTime
AUTO UPDATE
更新时间

2.3 索引设计和性能优化

-- 组织ID索引 (用于快速查询组织下的股东)
CREATE INDEX idx_shareholder_organization_id ON shareholder(organizationId);

-- 联系电话索引 (用于去重和搜索)
CREATE INDEX idx_shareholder_contact_number ON shareholder(contactNumber);

-- 复合索引 (组织+姓名，用于搜索功能)
CREATE INDEX idx_shareholder_org_name ON shareholder(organizationId, securitiesAccountName);

2.4 数据关系图

erDiagram
    Organization ||--o{ Shareholder : "拥有"
    Organization {
        string id PK
        string name
        datetime createdAt
    }
    Shareholder {
        string id PK
        string organizationId FK
        string securitiesAccountName
        string contactNumber
        datetime createdAt
        datetime updatedAt
    }

3. 接口设计

3.1 成员名单相关API接口清单

接口路径
方法
功能
权限要求
/api/meetings/create
POST
创建会议(含成员名单)
已认证用户
/api/meetings/update
PUT
更新会议(含成员名单)
会议创建者
/api/meetings/shareholders
GET
获取组织股东列表
组织成员

3.2 详细的接口参数、请求体、响应格式

3.2.1 创建会议接口

请求路径: `POST /api/meetings/create`

请求体:
interface CreateMeetingRequest {
  userid?: string;           // 用户ID (可选)
  subject: string;           // 会议主题
  type: number;              // 会议类型 (0:预约会议, 1:快速会议)
  start_time: string;        // 开始时间 ISO格式
  end_time: string;          // 结束时间 ISO格式
  password?: string;         // 会议密码
  guests?: Guest[];          // 成员名单 (最多2000人)
  settings?: MeetingSettings; // 会议设置
  time_zone?: string;        // 时区
  location?: string;         // 会议地点
}

interface Guest {
  area: string;              // 国家/地区代码 (如: "86")
  phone_number: string;      // 手机号
  guest_name?: string;       // 成员姓名
}

响应格式:
interface CreateMeetingResponse {
  success: boolean;
  data?: {
    meeting_id: string;      // 会议ID
    meeting_code: string;    // 会议号
    join_url: string;        // 加入链接
    subject: string;         // 会议主题
    start_time: string;      // 开始时间
    end_time: string;        // 结束时间
  };
  error?: string;            // 错误信息
}

3.2.2 获取股东列表接口

请求路径: `GET /api/meetings/shareholders`

查询参数:
interface GetShareholdersQuery {
  organizationId: string;    // 组织ID
  search?: string;           // 搜索关键词 (可选)
  page?: number;             // 页码 (默认1)
  pageSize?: number;         // 每页数量 (默认20)
}

响应格式:
interface GetShareholdersResponse {
  success: boolean;
  data?: {
    shareholders: ShareholderInfo[];
    pagination: {
      total: number;         // 总数量
      page: number;          // 当前页
      pageSize: number;      // 每页数量
      totalPages: number;    // 总页数
    };
  };
  error?: string;
}

interface ShareholderInfo {
  id: string;                      // 股东ID
  securitiesAccountName: string;   // 证券账户名称
  contactNumber: string;           // 联系电话
}

3.3 错误码定义和异常处理

错误码
HTTP状态码
错误信息
处理方式
VALIDATION_ERROR
400
表单验证失败
显示具体验证错误信息
GUEST_LIMIT_EXCEEDED
400
成员数量超过2000人限制
提示用户减少成员数量
INVALID_CONTACT_FORMAT
400
联系方式格式不正确
显示正确格式示例
UNAUTHORIZED
401
用户未认证
跳转到登录页面
FORBIDDEN
403
权限不足
显示权限不足提示
MEETING_NOT_FOUND
404
会议不存在
跳转到会议列表
THIRD_PARTY_API_ERROR
500
第三方API调用失败
显示重试提示
INTERNAL_SERVER_ERROR
500
服务器内部错误
显示通用错误提示

4. 第三方组件

4.1 使用的UI组件库

4.1.1 Shadcn UI + Radix UI 组件

组件名
用途
配置说明
Dialog
批量导入、系统导入对话框
支持键盘导航和焦点管理
Button
各种操作按钮
支持多种变体(outline, default等)
Input
成员姓名、联系方式输入
内置验证状态显示
Select
国家/地区代码选择
支持搜索和键盘导航
Form
表单容器和字段管理
集成React Hook Form
Table
股东列表展示
支持排序、分页、选择

4.1.2 Ant Design 组件

// 仅在股东列表中使用Ant Design Table组件
import { Table, ConfigProvider } from "antd";

// 配置中文本地化
const antdConfig = {
  locale: zhCN,
  theme: {
    token: {
      colorPrimary: '#3b82f6', // 与项目主题色保持一致
    }
  }
};

4.2 表单验证、文件上传等第三方库

4.2.1 表单管理和验证

// React Hook Form - 表单状态管理
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

// Zod - Schema验证
import { z } from "zod";

// 成员数据验证Schema
const guestSchema = z.object({
  area: z.string().min(1, "请选择国家/地区代码"),
  phone_number: z.string().min(1, "请输入联系方式"),
  guest_name: z.string().optional(),
}).refine((guest) => {
  if (!guest.phone_number.trim()) return true;
  const validation = validateContactFormat(guest.area, guest.phone_number);
  return validation.valid;
}, {
  message: "联系方式格式不正确",
  path: ["phone_number"],
});

4.2.2 文件上传处理

// React Dropzone - 文件拖拽上传
import { useDropzone } from "react-dropzone";

// XLSX - Excel文件解析
import * as XLSX from 'xlsx';

// 文件上传配置
const dropzoneConfig = {
  accept: {
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
    'application/vnd.ms-excel': ['.xls']
  },
  maxFiles: 1,
  maxSize: 10 * 1024 * 1024, // 10MB限制
};

4.3 各组件的功能说明和集成方式

4.3.1 MeetingGuestManagement 主组件

功能特性:
- 成员列表的增删改查
- 实时表单验证和错误提示
- 支持批量导入和系统导入
- 成员数量限制(2000人)和统计显示
集成方式:
<MeetingGuestManagement
  control={control}
  getValues={getValues}
  setValue={setValue}
  watch={watch}
  shouldShowGuestFeature={shouldShowGuestFeature}
  batchImportDialogOpen={batchImportDialogOpen}
  setBatchImportDialogOpen={setBatchImportDialogOpen}
  handleBatchImport={handleBatchImport}
  systemImportDialogOpen={systemImportDialogOpen}
  setSystemImportDialogOpen={setSystemImportDialogOpen}
  handleSystemImport={handleSystemImport}
  triggerValidation={triggerValidation}
/>

4.3.2 GuestBatchImportDialog 批量导入组件

功能特性:
- Excel文件格式验证
- 文件解析和数据验证
- 导入进度显示和错误处理
- 数量限制检查
集成方式:
<GuestBatchImportDialog
  isOpen={batchImportDialogOpen}
  onOpenChange={setBatchImportDialogOpen}
  onImportComplete={handleBatchImport}
  currentGuestCount={countValidGuests(guests)}
/>

4.3.3 GuestSystemImportDialog 系统导入组件

功能特性:
- 股东数据分页加载
- 搜索和筛选功能
- 批量选择和确认导入
- 数据去重处理
集成方式:
<GuestSystemImportDialog
  isOpen={systemImportDialogOpen}
  onOpenChange={setSystemImportDialogOpen}
  onImportComplete={handleSystemImport}
/>

5. 部署方案

5.1 服务器环境配置要求

5.1.1 基础环境
| Node.js | >= 20.0.0 | 支持最新ES特性和性能优化 |
| pnpm | >= 9.3.0 | 包管理器，支持workspace |
| PostgreSQL | >= 14.0 | 数据库，支持JSON字段 |
| Redis | >= 6.0 | 缓存和会话存储 |

5.2 部署流程和步骤

5.2.1 环境准备

# 1. 克隆代码仓库
git clone <repository-url>
cd supstar

# 2. 安装依赖
pnpm install

# 3. 配置环境变量
cp .env.example .env.local
# 编辑 .env.local 配置数据库连接等信息

5.2.2 数据库初始化

# 1. 生成Prisma客户端
pnpm db:generate

# 2. 运行数据库迁移
pnpm db:migrate

# 3. 初始化种子数据 (可选)
pnpm db:seed

5.2.3 构建和启动

pnpm id

5.3 环境变量配置

# 数据库配置
DATABASE_URL="postgresql://user:password@localhost:5432/database"

# 认证配置
BETTER_AUTH_SECRET="your-secret-key"
BETTER_AUTH_URL="https://your-domain.com"

# 腾讯会议配置相关参数
#appid,SDK id, secret id, secret key通过在腾讯会议后台“企业管理”，“企业自建应用”创建应用后获取具体值
TENCENT_APP_ID=""
TENCENT_SDK_ID=""""
TENCENT_SECRET_ID=""
TENCENT_SECRET_KEY=""
# operator id 为腾讯会议管理员账号的userid，用于高级权限 API 调用，如成员管理和获取会议信息
TENCENT_OPERATOR_ID=""

6. 开发分支管理

6.1 基于 f/meeting_v3 分支的开发流程

6.1.1 开发工作流

# 1. 从 f/meeting_v3 分支创建功能分支
git checkout f/meeting_v3
git pull origin f/meeting_v3

# 2. 开发和提交
git add .
git commit -m "feat: 优化成员名单验证逻辑"

# 3. 推送到远程分支
git push origin f/meeting_v3

# 4. 创建Pull Request到 f/meeting_v3 分支
# 5. 代码审查通过后合并dev分支

6.2 代码提交规范和合并策略

6.2.1 提交信息规范 (Conventional Commits)

# 功能开发
git commit -m "feat(guest): 添加批量导入Excel功能"

# 问题修复
git commit -m "fix(validation): 修复联系方式验证逻辑"

# 文档更新
git commit -m "docs(api): 更新成员管理API文档"

# 样式调整
git commit -m "style(ui): 调整成员列表布局样式"

# 重构代码
git commit -m "refactor(utils): 重构成员验证工具函数"

# 性能优化
git commit -m "perf(import): 优化大文件导入性能"

# 测试相关
git commit -m "test(guest): 添加成员验证单元测试"

6.2.2 合并策略
| feature → f/meeting_v3 | Squash Merge | 保持提交历史整洁 |
| f/meeting_v3 → dev | Merge Commit | 保留功能分支信息 |
| dev → main | Merge Commit | 保留版本发布信息 |

7. 单元测试

7.1 功能自测清单

7.1.1 成员管理核心功能

手动添加成员功能
[] 点击"添加成员"按钮能正常添加新行
[] 国家/地区代码下拉选择正常工作
[] 联系方式输入框支持手机号和固定电话格式
[] 成员姓名输入框正常工作
[] 删除按钮能正确移除对应成员行
[] 成员数量统计显示正确（已添加X人/最多2000人）
[] 达到2000人限制时，添加按钮被禁用

数据验证功能
[] 空的联系方式显示"请输入联系方式"错误
[] 无效手机号格式显示"联系方式格式不正确"错误
[] 空的成员姓名显示"请输入成员姓名"错误
[] 有效数据不显示任何错误提示
[] 实时验证：输入时立即显示/隐藏错误信息

Excel批量导入功能
[] 点击"Excel导入"按钮打开导入对话框
[] 拖拽Excel文件到上传区域正常工作
[] 点击上传区域能打开文件选择器
[] 只接受.xlsx和.xls格式文件
[] 超过10MB文件显示大小限制错误
[] 非Excel文件显示格式错误
[] 成功解析Excel文件并导入数据
[] 导入后成员列表正确更新
[] 导入数量超过2000人限制时显示错误

系统股东导入功能
[] 点击"系统导入"按钮打开股东选择对话框
[] 股东列表正常加载和显示
[] 搜索功能正常工作
[] 分页加载功能正常
[] 批量选择股东功能正常
[] 确认导入后成员列表正确更新
[] 导入的股东数据格式正确（姓名、联系方式）

7.1.2 边界条件和异常处理

数据边界测试
[] 成员数量为0时的显示状态
[] 成员数量接近2000时的行为
[] 成员数量达到2000时的限制提示
[] 超长姓名和联系方式的处理
[] 特殊字符在姓名中的处理

网络异常处理
[] 网络断开时的错误提示
[] API调用失败时的重试机制
[] 文件上传失败时的错误处理
[] 股东数据加载失败时的提示

7.2 手动测试指导

7.2.1 正常流程测试

测试场景1：手动添加成员
步骤：
1. 进入会议创建/编辑页面
2. 滚动到"设置成员名单"部分
3. 点击"添加成员"按钮
4. 选择国家代码"86"
5. 输入手机号"13800138000"
6. 输入姓名"张三"
7. 再次点击"添加成员"添加第二个成员

预期结果：
- 成功添加两行成员数据
- 成员数量显示"已添加2人/最多2000人"
- 所有输入字段正常工作
- 无错误提示显示

测试场景2：Excel批量导入
准备测试数据：创建Excel文件 guests.xlsx
| 国家代码 | 联系方式 | 成员姓名 |
|---------|----------|----------|
| 86      | 13800138000 | 张三 |
| 86      | 13900139000 | 李四 |
| 86      | 021-68499012 | 王五 |

步骤：
1. 点击"Excel导入"按钮
2. 拖拽准备好的Excel文件到上传区域
3. 等待文件解析完成
4. 确认导入结果

预期结果：
- 成功导入3位成员
- 成员列表显示导入的数据
- 显示"成功导入3位成员"提示
- 对话框自动关闭

7.2.2 异常流程测试

测试场景3：无效数据验证
步骤：
1. 添加成员但不填写任何信息
2. 输入无效手机号"123"
3. 输入过长的姓名（超过50个字符）
4. 尝试提交表单

预期结果：
- 空字段显示相应错误提示
- 无效手机号显示格式错误
- 过长姓名被截断或显示长度限制提示
- 表单提交被阻止

测试场景4：文件格式错误
步骤：
1. 点击"Excel导入"按钮
2. 尝试上传.txt文件
3. 尝试上传超过10MB的文件
4. 上传空的Excel文件

预期结果：
- .txt文件显示"请选择Excel文件"错误
- 大文件显示"文件大小不能超过10MB"错误
- 空文件显示"Excel文件中没有数据"错误

7.2.3 测试数据样例

有效Excel文件格式
文件名：valid_guests.xlsx
内容：
| 国家代码 | 联系方式 | 成员姓名 |
|---------|----------|----------|
| 86      | 13800138000 | 张三 |
| 86      | 13900139000 | 李四 |
| 86      | 021-68499012 | 王五 |
| 1       | 2125551234 | John Smith |

无效数据Excel文件
文件名：invalid_guests.xlsx
内容：
| 国家代码 | 联系方式 | 成员姓名 |
|---------|----------|----------|
| 86      | 123      | 张三 |    # 无效手机号
| abc     | 13900139000 | 李四 |  # 无效国家代码
|         | 13700137000 | 王五 |  # 空国家代码
| 86      |          | 赵六 |    # 空联系方式

7.3 E2E测试说明

7.3.1 Playwright测试配置

项目已配置Playwright进行端到端测试，配置文件位于 playwright.config.ts。

运行E2E测试
# 运行所有E2E测试
pnpm e2e

# 运行带UI界面的测试
pnpm e2e:ui

# 运行特定测试文件
pnpm exec playwright test meeting-guest-management.spec.ts

7.3.2 关键用户流程测试脚本

会议成员管理完整流程测试
// tests/meeting-guest-management.spec.ts
import { test, expect } from '@playwright/test';

test.describe('会议成员管理', () => {
  test.beforeEach(async ({ page }) => {
    // 登录并导航到会议创建页面
    await page.goto('/login');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password');
    await page.click('[data-testid="login-button"]');
    await page.goto('/meetings/create');
  });

  test('手动添加成员功能', async ({ page }) => {
    // 滚动到成员管理部分
    await page.scrollIntoView('[data-testid="guest-management"]');

    // 添加第一个成员
    await page.click('[data-testid="add-guest-button"]');
    await page.selectOption('[data-testid="area-select-0"]', '86');
    await page.fill('[data-testid="phone-input-0"]', '13800138000');
    await page.fill('[data-testid="name-input-0"]', '张三');

    // 验证成员数量统计
    await expect(page.locator('[data-testid="guest-count"]')).toContainText('已添加 1 人');

    // 添加第二个成员
    await page.click('[data-testid="add-guest-button"]');
    await page.selectOption('[data-testid="area-select-1"]', '86');
    await page.fill('[data-testid="phone-input-1"]', '13900139000');
    await page.fill('[data-testid="name-input-1"]', '李四');

    // 验证最终状态
    await expect(page.locator('[data-testid="guest-count"]')).toContainText('已添加 2 人');
  });

  test('Excel批量导入功能', async ({ page }) => {
    // 准备测试文件
    const filePath = './test-data/guests.xlsx';

    // 打开导入对话框
    await page.click('[data-testid="batch-import-button"]');

    // 上传文件
    await page.setInputFiles('[data-testid="file-upload"]', filePath);

    // 等待导入完成
    await expect(page.locator('[data-testid="import-success"]')).toBeVisible();

    // 验证导入结果
    await expect(page.locator('[data-testid="guest-count"]')).toContainText('已添加 3 人');
  });

  test('数据验证错误处理', async ({ page }) => {
    // 添加成员但输入无效数据
    await page.click('[data-testid="add-guest-button"]');
    await page.fill('[data-testid="phone-input-0"]', '123'); // 无效手机号

    // 验证错误提示
    await expect(page.locator('[data-testid="phone-error-0"]')).toContainText('联系方式格式不正确');

    // 修正数据
    await page.fill('[data-testid="phone-input-0"]', '13800138000');

    // 验证错误消失
    await expect(page.locator('[data-testid="phone-error-0"]')).not.toBeVisible();
  });
});

7.3.3 测试环境配置

环境变量配置
# .env.test
DATABASE_URL="postgresql://test:test@localhost:5432/test_db"
TENCENT_APP_ID="test_app_id"
TENCENT_SECRET_ID="test_secret_id"
TENCENT_SECRET_KEY="test_secret_key"
TENCENT_OPERATOR_ID="test_operator_id"

测试数据准备
# 准备测试数据库
pnpm db:reset --env test
pnpm db:seed --env test

# 准备测试文件
mkdir -p test-data
# 将测试用的Excel文件放入test-data目录

7.4 技术框架说明

7.4.1 当前项目测试工具栈

工具
用途
配置文件
| Playwright | E2E测试 | `playwright.config.ts` |
| Next.js内置测试 | 组件测试 | `next.config.ts` |
| TypeScript | 类型检查 | `tsconfig.json` |
| Biome | 代码检查和格式化 | `biome.json` |

7.4.2 快速自测命令

# 启动开发服务器
pnpm dev

# 类型检查
pnpm type-check

# 代码格式检查
pnpm lint

# 运行E2E测试
pnpm e2e

# 构建生产版本
pnpm build

7.4.3 自测最佳实践

1. 开发过程中的实时验证
- 保持开发服务器运行，实时查看更改效果
- 使用浏览器开发者工具检查控制台错误
- 定期运行TypeScript类型检查
2. 功能完成后的完整验证
- 按照功能自测清单逐项验证
- 使用不同浏览器测试兼容性
- 测试移动端响应式布局
3. 提交前的最终检查
- 运行完整的E2E测试套件
- 确保所有TypeScript类型检查通过
- 验证代码格式符合项目规范

---

总结

本技术方案详细阐述了会议管理系统中"设置成员名单"功能的完整技术实现方案，涵盖了从前端组件设计到后端API接口、从数据库设计到部署配置的各个方面。

核心特性:
- 支持手动添加、批量导入、系统导入三种成员添加方式
- 完善的数据验证和错误处理机制
- 高性能的文件解析和数据处理
- 用户友好的交互界面和反馈机制
技术亮点:
- 基于Next.js App Router的现代化架构
- TypeScript全栈类型安全
- Zod Schema驱动的数据验证
- 组件化和模块化的代码组织
该方案确保了功能的可扩展性、可维护性和高性能，为后续的功能迭代和系统优化奠定了坚实的技术基础。
