// 导入必要的依赖
import { getSession } from "@saas/auth/lib/server"; // 用于获取用户会话信息
import { SettingsList } from "@saas/shared/components/SettingsList"; // 设置列表组件，用于布局
import { ShareholderList } from "@saas/shareholder/components"; // 导入股东列表组件
import { redirect } from "next/navigation"; // 页面重定向功能
import { Suspense } from "react"; // 用于数据加载期间显示fallback UI
import { ShareholderRegistrySkeleton } from "@saas/shareholder/components/ShareholderRegistrySkeleton"; // 骨架屏组件

// 定义参数类型
type ShareholderParams = {
	organizationSlug: string;
};

/**
 * 生成页面元数据
 * 修改时间: 2025-07-16
 * 修改人：Miya
 */
export async function generateMetadata() {
	return {
		title: "股东数据",
	};
}




/**
 * 股东名册数据页面组件
 * 这是一个服务器组件，用于显示股东名册数据功能的页面
 */
export default async function ShareholderRegisterPage({
	params,
}: {
	params: ShareholderParams;
}) {
	// 获取用户会话信息，用于验证用户是否已登录
	const session = await getSession();

	// 如果用户未登录，重定向到登录页面
	if (!session) {
		return redirect("/auth/login");
	}

	// 由于 Next.js 路由系统要求 params 需要被 await，我们使用 Promise.resolve
	const { organizationSlug } = await Promise.resolve(params);

	// 渲染页面内容
	return (
		<SettingsList>
			{/* 使用Suspense和骨架屏组件提供更好的加载体验 */}
			<Suspense fallback={<ShareholderRegistrySkeleton />}>
				{/* 渲染股东列表组件，传入组织slug参数 */}
				<ShareholderList organizationSlug={organizationSlug} />
			</Suspense>
		</SettingsList>
	);
} 