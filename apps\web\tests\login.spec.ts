import { expect, test } from "@playwright/test";
import { config } from "@repo/config";
import { testEmail, testPassword } from "./config";

test.describe("login page", () => {
  // test("should load login form", async ({ page }) => {
  //   await page.goto("/auth/login");
    
  //   // 验证页面标题和表单元素存在
  //   // 使用更精确的选择器，不依赖于具体文本内容
  //   await expect(page.locator('h1')).toBeVisible();
    
  //   // 使用name属性或其他更可靠的选择器来定位输入框
  //   const emailInput = page.locator('input[autocomplete="email"]');
  //   await expect(emailInput).toBeVisible();
    
  //   // 密码输入框
  //   const passwordInput = page.locator('input[autocomplete="current-password"]');
  //   await expect(passwordInput).toBeVisible();
    
  //   // 提交按钮 - 使用type属性和角色
  //   const submitButton = page.locator('button[type="submit"]');
  //   await expect(submitButton).toBeVisible();
  // });

  // test("should show validation errors for empty fields", async ({ page }) => {
  //   await page.goto("/auth/login");
    
  //   // 不填写任何内容直接提交
  //   await page.getByRole("button", { name: /sign in/i }).click();
    
  //   // 验证表单验证错误
  //   await expect(page.getByText(/email.*required|invalid/i)).toBeVisible();
  // });

  test("should show error for invalid credentials", async ({ page }) => {
    await page.goto("/auth/login");
    
    // 填写错误的登录信息
    await page.locator('input[autocomplete="email"]').fill("<EMAIL>");
    await page.locator('input[autocomplete="current-password"]').fill("wrongpassword");
    await page.locator('button[type="submit"]').click();
    
    // 验证错误提示
    // 使用更精确的选择器，排除Next.js的路由通告器
    const errorAlert = page.locator('div[role="alert"]:not([id="__next-route-announcer__"])');
    await expect(errorAlert).toBeVisible();
    
    // 匹配可能的错误消息文本
    await expect(errorAlert).toContainText(/credentials.*invalid|invalid credentials|incorrect password|凭据无效/i);
  });

  test("should redirect after successful login", async ({ page }) => {
    // 这里需要使用有效的测试账号，或者在测试前创建一个测试账号
    // const testEmail = "<EMAIL>";
    // const testPassword = "9EzTgeOEQ_2XIPNT";
    
    await page.goto("/auth/login");
    
    // 填写正确的登录信息，使用更可靠的选择器
    await page.locator('input[autocomplete="email"]').fill(testEmail);
    await page.locator('input[autocomplete="current-password"]').fill(testPassword);
    await page.locator('button[type="submit"]').click();
    
    // 等待重定向完成
    await page.waitForURL(/\/app(\/.*)?$/, { timeout: 10000 });
    
    // 验证我们已经成功登录并进入了应用程序区域
    // 可以检查是否存在应用程序中特有的元素，如导航栏或用户菜单
    // await expect(page.locator('nav')).toBeVisible({ timeout: 5000 });
    
    // 或者验证URL是否符合预期模式
    const currentUrl = page.url();
    expect(currentUrl).toMatch(/\/app(\/.*)?$/);
  });

  // test("should toggle password visibility", async ({ page }) => {
  //   await page.goto("/auth/login");
    
  //   // 填写密码
  //   await page.getByLabel(/password/i).fill("somepassword");
    
  //   // 初始状态应该是密码隐藏的
  //   const passwordInput = page.getByLabel(/password/i);
  //   await expect(passwordInput).toHaveAttribute("type", "password");
    
  //   // 点击显示密码按钮
  //   await page.getByRole("button", { name: /show password/i }).click();
    
  //   // 验证密码现在可见
  //   await expect(passwordInput).toHaveAttribute("type", "text");
    
  //   // 再次点击隐藏密码
  //   await page.getByRole("button", { name: /hide password/i }).click();
    
  //   // 验证密码再次隐藏
  //   await expect(passwordInput).toHaveAttribute("type", "password");
  // });

  test("should switch to magic link mode and send login link", async ({ page }) => {
    // 仅当两种登录模式都启用时才运行此测试
    test.skip(!config.auth.enableMagicLink || !config.auth.enablePasswordLogin);
    
    await page.goto("/auth/login");
    
    // 切换到魔法链接模式 - 使用更精确的选择器
    // 查找包含"魔法链接"或"magic link"文本的按钮或标签
    await page.locator('button[role="tab"]').filter({ hasText: /magic link|魔法链接/i }).click();
    
    // 验证密码字段不可见
    await expect(page.locator('input[autocomplete="current-password"]')).not.toBeVisible();
    
    // 填写邮箱
    const testEmail = "<EMAIL>";
    await page.locator('input[autocomplete="email"]').fill(testEmail);
    
    // 点击发送魔法链接按钮 - 使用精确的选择器
    // 使用表单中的提交按钮
    await page.locator('form button[type="submit"]').click();
    
    // 验证成功提示消息
    const successAlert = page.locator('div[role="alert"]:not([id="__next-route-announcer__"])');
    await expect(successAlert).toBeVisible({ timeout: 5000 });
    await expect(successAlert).toContainText(/link sent|已发送|邮件已发送/i);
  });

  test("should handle forgot password flow", async ({ page }) => {
    // 从登录页面开始
    await page.goto("/auth/login");
    
    // 点击"忘记密码"链接 - 使用更精确的选择器
    await page.locator('a[href="/auth/forgot-password"]').click();
    
    // 验证已导航到忘记密码页面
    await expect(page).toHaveURL(/\/auth\/forgot-password/);
    
    // 验证忘记密码表单存在
    await expect(page.locator('h1')).toBeVisible();
    
    // 填写邮箱
    const testEmail = "<EMAIL>";
    await page.locator('input[autocomplete="email"]').fill(testEmail);
    
    // 提交表单 - 尝试多种选择器组合
    // 1. 使用按钮文本内容（可能受国际化影响）
    // await page.getByRole('button', { name: /submit|reset|send|提交|重置|发送/i }).click();
    
    // 2. 使用表单内的第一个按钮
    await page.locator('form button').first().click();
    
    // 3. 如果上面的方法都不起作用，可以尝试使用更具体的CSS选择器
    // await page.locator('form button.bg-primary, form button.bg-blue-600').click();
    
    // 4. 或者使用XPath（不推荐，但在其他方法都失败时可以尝试）
    // await page.locator('//form//button[contains(@class, "bg-primary") or contains(text(), "Submit")]').click();
    
    // 验证成功提示消息
    const successAlert = page.locator('div[role="alert"]:not([id="__next-route-announcer__"])');
    await expect(successAlert).toBeVisible({ timeout: 5000 });
    await expect(successAlert).toContainText(/link sent|已发送|邮件已发送/i);
    
    // 验证"返回登录"链接存在
    await page.locator('a[href="/auth/login"]').click();
    
    // 验证已返回登录页面
    await expect(page).toHaveURL(/\/auth\/login/);
  });
});












