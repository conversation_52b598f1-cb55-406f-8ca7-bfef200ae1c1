"use client";

import { useState, useEffect } from "react";
import { useSession } from "../../auth/hooks/use-session";

export interface UserAccountTypeResult {
  accountType: number | null;
  isLoading: boolean;
  error: string | null;
  isGuestFeatureAllowed: boolean;
}

/**
 * 用户账户类型查询 Hook
 * 
 * 调用 /api/meetings/users 接口获取当前用户的账户类型信息
 * 根据 account_type 字段判断是否允许使用会议嘉宾功能
 * 
 * @returns {UserAccountTypeResult} 包含账户类型、加载状态、错误信息和嘉宾功能权限的对象
 */
export function useUserAccountType(): UserAccountTypeResult {
  const { user } = useSession();
  const [accountType, setAccountType] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // 如果用户信息不存在，直接返回
    if (!user?.id) {
      setAccountType(null);
      setIsLoading(false);
      setError(null);
      return;
    }

    const fetchUserAccountType = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/meetings/users?userId=${user.id}`);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        
        if (!data.success) {
          throw new Error(data.error?.message || data.error || "获取用户账户类型失败");
        }

        // 从响应中提取账户类型
        const userAccountType = data.exists?.account_type;
        setAccountType(userAccountType || null);
        
      } catch (err: any) {
        console.error("获取用户账户类型失败:", err);
        setError(err.message || "获取用户账户类型失败");
        setAccountType(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserAccountType();
  }, [user?.id]);

  // 根据账户类型判断是否允许使用嘉宾功能
  // account_type 等于 2 时不允许使用嘉宾功能
  const isGuestFeatureAllowed = accountType !== null && accountType !== 2;

  return {
    accountType,
    isLoading,
    error,
    isGuestFeatureAllowed,
  };
}
