/**
 * 投资人收藏功能模块
 *
 * @fileoverview 提供投资人标签的创建、删除和收藏状态切换功能，支持加密通信
 * <AUTHOR>
 * @since 2025-07-22
 * @version 1.0.0
 *
 * 功能特性：
 * - 投资人标签创建（收藏功能）
 * - 投资人标签删除（取消收藏）
 * - 收藏状态切换
 * - 请求数据加密和响应解密
 * - 完整的错误处理机制
 * - TypeScript类型安全
 */

import {
	encryptRequestData,
	generateSign,
	decryptData,
} from "@repo/utils/lib/crypto";

/**
 * 创建投资人标签（收藏功能）接口参数类型
 */
export interface CreateInvestorTagRequest {
	/** 组织ID */
	organizationId: string;
	/** 公司筛选ID */
	companyFilterId: string;
	/** 投资人代码 */
	investorCode: string;
	/** 标签名称 */
	tagName: string;
	/** 标签类别：系统标签或用户标签 */
	tagCategory: "system" | "user";
	/** 标签元数据，包含颜色、图标等可选属性 */
	tagMetadata?: {
		/** 标签颜色 */
		color?: string;
		/** 标签图标 */
		icon?: string;
		/** 其他扩展属性 */
		[key: string]: any;
	};
}

/**
 * 删除投资人标签（取消收藏）接口参数类型
 */
export interface DeleteInvestorTagRequest {
	/** 组织ID */
	organizationId: string;
	/** 投资人代码 */
	investorCode: string;
}

/**
 * API响应通用类型
 * @template T 响应数据的类型
 */
export interface ApiResponse<T = any> {
	/** 响应状态码 */
	code: number;
	/** 响应消息 */
	message: string;
	/** 响应数据 */
	data: T;
}

/**
 * 创建标签响应数据类型
 */
export interface CreateTagResponseData {
	/** 创建的标签ID */
	id: string;
}

/**
 * 删除标签响应数据类型
 */
export interface DeleteTagResponseData {
	/** 删除的标签ID */
	id: string;
}

/**
 * 创建投资人标签（收藏功能）
 *
 * 通过加密通信方式向服务器发送创建标签请求，实现投资人收藏功能
 *
 * @param params 创建标签的参数，包含组织ID、投资人代码、标签信息等
 * @returns Promise<ApiResponse<CreateTagResponseData>> 返回创建结果，包含新标签的ID
 *
 * @throws {Error} 当请求失败、响应错误或解密失败时抛出错误
 *
 * @example
 * ```typescript
 * const result = await createInvestorTag({
 *   organizationId: "org123",
 *   companyFilterId: "filter456",
 *   investorCode: "INV001",
 *   tagName: "收藏",
 *   tagCategory: "user"
 * });
 * ```
 */
export async function createInvestorTag(
	params: CreateInvestorTagRequest
): Promise<ApiResponse<CreateTagResponseData>> {
	try {
		// 1. 参数加密（需要 await）
		const encrypted = await encryptRequestData(params);

		// 2. 生成签名（使用加密后的内容）
		const sign = generateSign(encrypted);

		// 3. 发送POST请求
		const requestBody = { content: encrypted, sign };

		const response = await fetch("/api/investor-management/tags/create", {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				"X-Sign": sign,
			},
			body: JSON.stringify(requestBody),
		});

		if (!response.ok) {
			const errorText = await response.text();
			throw new Error(`请求失败: ${response.status} - ${errorText}`);
		}

		// 4. 解密响应（需先 parse 再断言类型）
		const result = await response.json();

		if (result.code !== 200) {
			throw new Error(result.message || "接口返回错误");
		}

		// decryptData 返回 string，需 JSON.parse
		const data = JSON.parse(decryptData(result.data)) as CreateTagResponseData;

		return {
			code: result.code,
			message: result.message,
			data: data,
		};
	} catch (error) {
		//bug:错误扑捉
		throw new Error(
			error instanceof Error ? error.message : "创建投资人标签失败",
		);
	}
}

/**
 * 删除投资人标签（取消收藏）
 *
 * 通过加密通信方式向服务器发送删除标签请求，实现取消收藏功能
 *
 * @param params 删除标签的参数，包含组织ID和投资人代码
 * @returns Promise<ApiResponse<DeleteTagResponseData>> 返回删除结果，包含被删除标签的ID
 *
 * @throws {Error} 当请求失败、响应错误或解密失败时抛出错误
 *
 * @example
 * ```typescript
 * const result = await deleteInvestorTag({
 *   organizationId: "org123",
 *   investorCode: "INV001"
 * });
 * ```
 */
export async function deleteInvestorTag(
	params: DeleteInvestorTagRequest
): Promise<ApiResponse<DeleteTagResponseData>> {
	try {
		// 1. 参数加密（需要 await）
		const encrypted = await encryptRequestData(params);

		// 2. 生成签名（使用加密后的内容）
		const sign = generateSign(encrypted);

		// 3. 发送POST请求
		const requestBody = { content: encrypted, sign };

		const response = await fetch("/api/investor-management/tags/delete", {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				"X-Sign": sign,
			},
			body: JSON.stringify(requestBody),
		});

		if (!response.ok) {
			const errorText = await response.text();
			throw new Error(`请求失败: ${response.status} - ${errorText}`);
		}

		// 4. 解密响应（需先 parse 再断言类型）
		const result = await response.json();

		if (result.code !== 200) {
			throw new Error(result.message || "接口返回错误");
		}

		// decryptData 返回 string，需 JSON.parse
		const data = JSON.parse(decryptData(result.data)) as DeleteTagResponseData;

		return {
			code: result.code,
			message: result.message,
			data: data,
		};
	} catch (error) {
		throw new Error(
			error instanceof Error ? error.message : "删除投资人标签失败"
		);
	}
}

/**
 * 切换收藏状态（收藏/取消收藏）
 *
 * 根据当前收藏状态自动选择创建或删除标签操作，提供便捷的收藏切换功能
 *
 * @param organizationId 组织ID
 * @param companyFilterId 公司筛选ID，仅在添加收藏时使用
 * @param investorCode 投资人代码
 * @param isFavorited 当前是否已收藏，true表示已收藏需要取消，false表示未收藏需要添加
 * @returns Promise<ApiResponse> 返回操作结果
 *
 * @throws {Error} 当底层创建或删除操作失败时抛出错误
 *
 * @example
 * ```typescript
 * // 添加收藏
 * await toggleFavoriteStatus("org123", "filter456", "INV001", false);
 *
 * // 取消收藏
 * await toggleFavoriteStatus("org123", "filter456", "INV001", true);
 * ```
 */
export async function toggleFavoriteStatus(
	organizationId: string,
	companyFilterId: string,
	investorCode: string,
	isFavorited: boolean
): Promise<ApiResponse> {
	if (isFavorited) {
		// 取消收藏 - 直接使用投资人代码
		return await deleteInvestorTag({
			organizationId,
			investorCode,
		});
	}

	// 添加收藏 - 创建用户标签
	return await createInvestorTag({
		organizationId,
		companyFilterId,
		investorCode,
		tagName: "收藏",
		tagCategory: "user",
		tagMetadata: {
			color: "#ff6b6b", // 红色收藏标签
			icon: "star",     // 星形图标
		},
	});
}

