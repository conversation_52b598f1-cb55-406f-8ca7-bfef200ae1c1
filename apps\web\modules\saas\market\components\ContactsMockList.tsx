/**
 * 联系人模拟列表组件
 *
 * @fileoverview 主要的基金投资人展示和管理组件，提供股票代码设置、基金数据展示和收藏功能
 * <AUTHOR>
 * @since 2025-07-22
 * @version 1.0.0
 *
 * 功能特性：
 * - 公司股票代码管理（本司代码和对标代码）
 * - 基金投资人数据展示
 * - 收藏功能管理
 * - 分页和懒加载
 * - 实时数据刷新
 * - 响应式设计
 *
 * 数据流程：
 * 1. 查询公司代码配置
 * 2. 根据代码查询序列数据（基金列表）
 * 3. 懒加载基金详细信息
 * 4. 支持收藏状态管理
 */

"use client";
import { useState, useEffect, useMemo } from "react";
import ContextCard from "./ContextCard";
import { Input } from "@ui/components/input";
import { Button } from "@ui/components/button";
import { Skeleton } from "@ui/components/skeleton";
import { SquarePen, RotateCcw, Save } from "lucide-react";
import { cn } from "@ui/lib";
// import { AILogo } from "@saas/market/components/AILogo";
// 弃用现在机器人AI组件 修改人：Miya 修改时间：2025/7/22
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useCode, useUpdateCompanyCode } from "../hooks/useCode";
import { useSequenceData } from "../hooks/useSequence";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
	TooltipPortal
} from "@ui/components/tooltip";

/**
 * 联系人模拟列表主组件
 *
 * 提供基金投资人数据的展示和管理功能，包括股票代码设置、数据查询、收藏管理等
 *
 * @returns 联系人模拟列表组件
 *
 * @example
 * ```tsx
 * <ContactsMockList />
 * ```
 */
export default function ContactsMockList() {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id || "";

	// 查询公司代码
	const { data: codeData, isLoading: codeLoading } = useCode(
		{ organizationId },
		{ enabled: !!organizationId },
	);
	// 新增：更新公司代码 mutation
	const updateMutation = useUpdateCompanyCode();

	// 第一步：查询序列数据（卡片容器、tags、收藏状态）
	// 接口：/api/n8n_proxy/fund_inquiry_test
	// 只有当有公司代码时才启用查询
	const hasCompanyCode = codeData?.companyFilter?.companyCode;
	const {
		data: sequenceData,
		isLoading: sequenceLoading,
		refetch: refetchSequence,
	} = useSequenceData({ organizationId }, { enabled: !!organizationId && !!hasCompanyCode });



	const [isEditing, setIsEditing] = useState(false);

	// 输入框状态管理
	const [companyCode, setCompanyCode] = useState("");
	const [benchmarkCodes, setBenchmarkCodes] = useState("");
	// 新增：错误提示状态
	// 刷新状态
	const [isRefreshing, setIsRefreshing] = useState(false);
	const [errorMessage, setErrorMessage] = useState("");

	// 收藏过滤状态
	const [activeFilter, setActiveFilter] = useState<"all" | "starred" | "unstarred">("all");

	// 本地收藏状态管理 - 用于立即更新UI显示
	const [localFavoriteCards, setLocalFavoriteCards] = useState<Set<string>>(new Set());

	// 记录上次请求时的收藏状态快照 - 用于检测收藏变动
	const [lastFetchFavoriteSnapshot, setLastFetchFavoriteSnapshot] = useState<Set<string>>(new Set());

	// 记录是否应该允许基金数据请求（初次加载或有收藏变化时为true）
	const [shouldAllowFundDataFetch, setShouldAllowFundDataFetch] = useState<boolean>(true);

	// 基金数据请求的强制刷新标识，用于强制React Query重新请求
	const [fundDataRefreshKey, setFundDataRefreshKey] = useState<number>(Date.now());

	// 检测收藏状态是否发生变化
	const hasFavoriteChanges = useMemo(() => {
		if (localFavoriteCards.size !== lastFetchFavoriteSnapshot.size) {
			return true;
		}

		// 检查是否有不同的元素
		const localArray = Array.from(localFavoriteCards);
		const snapshotArray = Array.from(lastFetchFavoriteSnapshot);

		for (const code of localArray) {
			if (!lastFetchFavoriteSnapshot.has(code)) {
				return true;
			}
		}

		for (const code of snapshotArray) {
			if (!localFavoriteCards.has(code)) {
				return true;
			}
		}

		return false;
	}, [localFavoriteCards, lastFetchFavoriteSnapshot]);

	// 处理收藏tab切换，只有在收藏状态发生变化时才重新请求fund_inquiry_test
	const handleFilterChange = async (filter: "all" | "starred" | "unstarred") => {
		setActiveFilter(filter);

		// 只有在收藏状态发生变化时才重新请求序列数据
		if (hasCompanyCode && hasFavoriteChanges) {
			// console.log('检测到收藏状态变化，重新请求序列数据...');
			try {
				await refetchSequence();
				// 更新快照
				setLastFetchFavoriteSnapshot(new Set(localFavoriteCards));
			} catch (error) {
				// console.error('切换收藏tab时重新请求数据失败:', error);
			}
		} else {
			// console.log('收藏状态无变化，使用缓存的序列数据');
		}

		// 基金经理数据请求：只有切换到"已收藏"和"未收藏"时才重新请求，"全部"不需要
		if (filter === "starred" || filter === "unstarred") {
			// console.log(`切换到${filter === "starred" ? "已收藏" : "未收藏"}tab，重新请求基金经理数据...`);
			setShouldAllowFundDataFetch(true);

			// 强制触发基金数据重新加载：更新刷新键，让React Query认为这是新查询
			setFundDataRefreshKey(Date.now());
		} else {
			// console.log('切换到全部tab，使用缓存的基金经理数据');
			// 对于"全部"tab，不重新请求基金经理数据，使用现有缓存
		}
	};

	// 处理收藏状态变化的回调 - 供ContextCard调用
	const handleFavoriteChange = (investorCode: string, isFavorited: boolean) => {
		setLocalFavoriteCards(prev => {
			const newSet = new Set(prev);
			if (isFavorited) {
				newSet.add(investorCode);
			} else {
				newSet.delete(investorCode);
			}
			return newSet;
		});
	};

	// 首次进入页面拿到公司代码、对标公司代码数据时自动赋值，并触发序列数据查询
	useEffect(() => {
		if (codeData?.companyFilter) {
			setCompanyCode(codeData.companyFilter.companyCode || "");
			setBenchmarkCodes(
				codeData.companyFilter.benchmarkCompanyCodes || "",
			);

			// 如果有公司代码，自动触发序列数据查询
			if (codeData.companyFilter.companyCode) {
				// useSequenceData 的 enabled 条件会自动触发查询
			}
		}
	}, [codeData]);


	// 初始化本地收藏状态 - 基于服务器数据
	useEffect(() => {
		if (sequenceData && Array.isArray(sequenceData)) {
			const favoriteSet = new Set<string>();
			sequenceData.forEach((item) => {
				// 检查用户标签且标签名包含"收藏"的项目
				if (item.tagCategory === "user" && item.tagName?.includes("收藏")) {
					favoriteSet.add(item.investorCode);
				}
			});
			setLocalFavoriteCards(favoriteSet);
			// 同时更新快照，表示这是最新的服务器状态
			setLastFetchFavoriteSnapshot(new Set(favoriteSet));
			// 初始化时允许基金数据请求
			setShouldAllowFundDataFetch(true);
		}
	}, [sequenceData]);

	// 根据收藏tab过滤条件显示序列数据
	// 使用 useMemo 确保计算结果稳定，避免 ContextCard 中的 hooks 调用顺序变化
	const displaySequenceData = useMemo(() => {
		// 每次都基于完整的sequenceData进行处理（fund_inquiry_test返回的全部数据）
		// 确保 sequenceData 是数组类型，如果不是则使用空数组
		const allData = Array.isArray(sequenceData) ? sequenceData : [];

		// 先按 investorCode 去重，合并标签信息
		const deduplicatedData = (() => {
			const itemMap = new Map<string, typeof allData[0]>();

			allData.forEach(item => {
				const key = item.investorCode;
				if (!key) {
					return;
				}

				const existing = itemMap.get(key);
				if (!existing) {
					itemMap.set(key, item);
				} else {
					// 智能合并标签信息，避免重复
					const existingTags = existing.tagName ? existing.tagName.split(',').map(tag => tag.trim()) : [];
					const newTags = item.tagName ? item.tagName.split(',').map(tag => tag.trim()) : [];

					// 合并并去重标签，保持特定顺序：持有本司 -> 持有对标 -> 收藏
					const allTags = [...existingTags, ...newTags];
					const uniqueTags = Array.from(new Set(allTags));

					// 按业务优先级排序
					const sortedTags = uniqueTags.sort((a, b) => {
						const priority: Record<string, number> = { '持有本司': 1, '持有对标': 2, '收藏': 3 };
						return (priority[a] || 999) - (priority[b] || 999);
					});

					const combinedTagName = sortedTags.join(',');

					// 如果新项目包含"收藏"，则使用新项目作为主要项目
					if (item.tagName?.includes("收藏")) {
						itemMap.set(key, {
							...item,
							tagName: combinedTagName,
						});
					} else {
						// 否则更新现有项目的标签
						itemMap.set(key, {
							...existing,
							tagName: combinedTagName,
						});
					}
				}
			});

			return Array.from(itemMap.values());
		})();

		// 过滤逻辑：只显示有持股关系的基金，单纯收藏（无持股关系）的基金不显示
		const filteredData = deduplicatedData.filter(item => {
			const tagName = item.tagName || '';
			const hasHolding = tagName.includes("持有本司") || tagName.includes("持有对标");

			// 显示条件：必须有持股关系（持有本司 或 持有对标）
			// 单纯的收藏（没有持股关系）不显示
			return hasHolding;
		});

		// 按 investorCode 排序，确保卡片位置稳定
		const sortedData = filteredData.sort((a, b) => {
			return a.investorCode.localeCompare(b.investorCode);
		});

		// 根据收藏tab过滤数据，传递给ContextCard：
		// 使用本地收藏状态进行过滤，实现立即响应的UI更新
		// - "all": 传递所有数据，ContextCard会请求所有基金的经理数据
		// - "starred": 只传递本地收藏状态中的数据，ContextCard只请求收藏基金的经理数据
		// - "unstarred": 只传递本地未收藏状态中的数据，ContextCard只请求未收藏基金的经理数据
		switch (activeFilter) {
			case "starred":
				// 只传递本地收藏状态中的项目，按收藏时间排序
				return sortedData
					.filter(item => localFavoriteCards.has(item.investorCode))
					.sort((a, b) => {
						// 按修改时间倒序排序（最新收藏的在前）
						return new Date(b.modifiedAt).getTime() - new Date(a.modifiedAt).getTime();
					});
			case "unstarred":
				// 只传递本地未收藏状态中的项目，保持原有排序
				return sortedData.filter(item => !localFavoriteCards.has(item.investorCode));
			default:
				// "all": 传递所有项目，保持按 investorCode 排序
				return sortedData;
		}
	}, [sequenceData, activeFilter, localFavoriteCards]);

// 刷新处理函数
const handleRefresh = async (clearCache = true) => {
	if (!companyCode || isRefreshing) {
		return;
	}

	setIsRefreshing(true);
	setErrorMessage("");

	try {
		// 刷新时允许基金数据请求
		setShouldAllowFundDataFetch(true);

		// 重新获取序列数据（fund_inquiry_test）
		await refetchSequence();

		// 刷新后更新快照，因为数据是最新的
		setLastFetchFavoriteSnapshot(new Set(localFavoriteCards));

		// 刷新按钮触发时，重置懒加载状态到初始状态
		// 这样用户可以从头开始浏览数据，提升性能
		if (clearCache) {
			// 通过滚动到顶部来触发懒加载重置
			window.scrollTo({ top: 0, behavior: 'smooth' });
		}
	} catch (error) {
		setErrorMessage("刷新失败，请重试");
	} finally {
		setIsRefreshing(false);
	}
};

// 渲染内容的条件判断
const renderContent = () => {
	// 只有在非编辑状态下且没有公司代码时才显示"暂无数据"
	// 编辑状态下即使删除了代码也继续显示原有数据
	if (!companyCode && !isEditing) {
		return (
			<div className="flex flex-col items-center justify-center h-40 text-gray-400 dark:text-gray-300 text-lg">
				暂无数据，请先填入公司股票代码
			</div>
		);
	}

	if (codeLoading) {
		return (
			<div className="flex flex-col items-center justify-center h-40 text-gray-400 dark:text-gray-300 text-lg">
				正在获取公司代码信息...
			</div>
		);
	}

	if (sequenceLoading) {
		return (
			<div className="flex flex-col items-center justify-center h-40 text-gray-400 dark:text-gray-300 text-lg">
				正在加载股票数据...
			</div>
		);
	}

	if (!sequenceData || sequenceData.length === 0) {
		return (
			<div className="flex flex-col items-center justify-center h-40 text-gray-400 dark:text-gray-300 text-lg">
				暂无股票数据
			</div>
		);
	}

	return (
		<ContextCard
			sequenceData={displaySequenceData}
			onRefresh={handleRefresh}
			organizationId={organizationId}
			companyFilterId={codeData?.companyFilter?.id || ""}
			onFavoriteChange={handleFavoriteChange}
			favoriteCards={localFavoriteCards}
			enableFundDataFetch={shouldAllowFundDataFetch}
			fundDataRefreshKey={fundDataRefreshKey}
		/>
	);
};

	return (
		<>
			{/* 删除机器人组件  修改人：Miya 修改时间：2025/7/22*/}
			{/* 输入框区域：两个输入框+编辑和刷新按钮 */}
			<p className="mt-2 text-center text-3xl md:text-3xl lg:text-4xl font-medium text-gray-900 dark:text-gray-100 mb-8">
				在这里，寻找最合适的投资人
			</p>
			{/* 增加文字区域 修改人：Miya 修改时间：2025/7/22 */}
			<div className="flex items-center gap-3 w-full mb-4 mt-2">
				{/* 公司代码输入框（较小） */}
				<div className="relative w-120">
					{codeLoading ? (
						<Skeleton className="h-11 w-full rounded-md" />
					) : (
						<Input
							type="text"
							placeholder={isEditing ? "输入公司股票代码" : ""}
							value={companyCode || ""}
							onChange={(e) => {
								setCompanyCode(e.target.value);
								if (errorMessage) {
									setErrorMessage("");
								}
							}}
							readOnly={!isEditing}
							className={cn(
								"h-11 pl-4 pr-4 py-4 rounded-md shadow border text-md focus:outline-none",
								!isEditing
									? "bg-white dark:bg-zinc-800 border-gray-200 dark:border-zinc-700 text-gray-700 dark:text-gray-300 cursor-default focus:ring-0"
									: "bg-white dark:bg-zinc-900 border-gray-200 dark:border-zinc-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:ring-2 focus:ring-primary",
							)}
						/>
					)}
				</div>
				{/* 对标公司代码输入框（较大） */}
				<div className="relative w-200">
					{codeLoading ? (
						<Skeleton className="h-11 w-full rounded-md" />
					) : (
						<Input
							type="text"
							placeholder={
								isEditing
									? "输入对标公司股票代码（多个用逗号隔开）"
									: ""
							}
							value={benchmarkCodes || ""}
							onChange={(e) => {
								setBenchmarkCodes(e.target.value);
								if (errorMessage) {
									setErrorMessage("");
								}
							}}
							readOnly={!isEditing}
							className={cn(
								"h-11 pl-4 pr-4 py-4 rounded-md shadow border text-md focus:outline-none",
								!isEditing
									? "bg-white dark:bg-zinc-800 border-gray-200 dark:border-zinc-700 text-gray-700 dark:text-gray-300 cursor-default focus:ring-0"
									: "bg-white dark:bg-zinc-900 border-gray-200 dark:border-zinc-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:ring-2 focus:ring-primary",
							)}
						/>
					)}
				</div>
				{/* 编辑和刷新按钮 */}
				{codeLoading ? (
					<>
						<Skeleton className="h-11 w-11 rounded-md" />
						<Skeleton className="h-11 w-11 rounded-md" />
					</>
				) : (
					<>
						<TooltipProvider>
							<Tooltip>
								<TooltipTrigger asChild>
									<Button
										variant="ghost"
										className="h-11 w-11 p-0 flex items-center justify-center shadow-none hover:bg-transparent focus:bg-transparent active:bg-transparent"
										onClick={() => {
											if (isEditing) {
												// 自动添加后缀的函数
												const addSuffix = (
													code: string,
												): string => {
													// 如果已经有后缀，直接返回
													if (code.includes(".")) {
														return code;
													}
													// 根据开头数字自动添加后缀
													if (
														code.startsWith("0") ||
														code.startsWith("3")
													) {
														return `${code}.SZ`;
													}
													if (code.startsWith("6")) {
														return `${code}.SH`;
													}
													return code; // 其他情况保持原样
												};

												// 校验基础股票代码格式（6位数字）
												const baseCodePattern =
													/^\d{6}$/;
												const fullCodePattern =
													/^\d{6}\.[a-zA-Z]+$/;

												// 处理公司股票代码
												let processedCompanyCode =
													companyCode.trim();
												if (
													!baseCodePattern.test(
														processedCompanyCode,
													) &&
													!fullCodePattern.test(
														processedCompanyCode,
													)
												) {
													setErrorMessage(
														"公司股票代码格式错误，应为6位数字，系统将自动添加后缀",
													);
													return;
												}
												// 自动添加后缀
												processedCompanyCode =
													addSuffix(
														processedCompanyCode,
													);

												// 处理对标公司股票代码（可选）
												let processedCodes: string[] =
													[];
												if (benchmarkCodes?.trim()) {
													const codes = benchmarkCodes
														.replace(/，/g, ",")
														.split(",")
														.map((s) => s.trim())
														.filter(Boolean);

													// 校验每个对标公司代码
													for (const code of codes) {
														if (
															!baseCodePattern.test(
																code,
															) &&
															!fullCodePattern.test(
																code,
															)
														) {
															setErrorMessage(
																"对标公司股票代码格式错误，应为6位数字，系统将自动添加后缀",
															);
															return;
														}
													}

													// 自动添加后缀
													processedCodes = codes.map(
														(code) =>
															addSuffix(code),
													);
												}

												// 保存：锁定输入框并发起更新请求
												// 将后缀转换为大写
												const normalizedCompanyCode =
													processedCompanyCode.replace(
														/\.([a-z]+)$/i,
														(_, suffix) =>
															`.${suffix.toUpperCase()}`,
													);
												const normalizedCodes =
													processedCodes.map((code) =>
														code.replace(
															/\.([a-z]+)$/i,
															(_, suffix) =>
																`.${suffix.toUpperCase()}`,
														),
													);

												const value =
													normalizedCodes.length > 0
														? normalizedCodes.join(
																",",
															)
														: "";

												// 更新输入框显示
												setCompanyCode(
													normalizedCompanyCode,
												);
												setBenchmarkCodes(value);

												updateMutation.mutate(
													{
														organizationId,
														companyCode:
															normalizedCompanyCode,
														benchmarkCompanyCodes:
															value,
													},
													{
														onSuccess: () => {
															// 保存成功后重新获取序列数据，并清除基金数据缓存
															handleRefresh(true);
														},
													},
												);
												setIsEditing(false);
											} else {
												// 编辑：解锁输入框
												setIsEditing(true);
											}
										}}
									>
										{isEditing ? (
											<Save className="w-6 h-6 text-gray-600 dark:text-gray-300" />
										) : (
											<SquarePen className="w-6 h-6 text-gray-600 dark:text-gray-300" />
										)}
									</Button>
								</TooltipTrigger>
								<TooltipPortal>
									<TooltipContent side="top" align="center">
										{isEditing ? "保存" : "编辑"}
									</TooltipContent>
								</TooltipPortal>
							</Tooltip>
						</TooltipProvider>
						<TooltipProvider>
							<Tooltip>
								<TooltipTrigger asChild>
									<Button
										variant="ghost"
										disabled={!companyCode || isRefreshing}
										onClick={() => handleRefresh(true)}
										className={cn(
											"h-11 w-11 p-0 flex items-center justify-center shadow-none hover:bg-transparent focus:bg-transparent active:bg-transparent",
											(!companyCode || isRefreshing) &&
												"opacity-50 cursor-not-allowed",
										)}
									>
										<RotateCcw
											className={cn(
												"w-6 h-6 text-gray-600 dark:text-gray-300",
												isRefreshing && "animate-spin",
											)}
										/>
									</Button>
								</TooltipTrigger>
								<TooltipPortal>
									<TooltipContent side="top" align="center">
										刷新
									</TooltipContent>
								</TooltipPortal>
							</Tooltip>
						</TooltipProvider>
					</>
				)}
			</div>
			{/* 错误提示条 */}
			{errorMessage && (
				<div className="mb-3 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
					<p className="text-red-600 dark:text-red-400 text-sm text-center">
						{errorMessage}
					</p>
				</div>
			)}

			{/* 筛选按钮 */}
			{(companyCode || codeLoading || (isEditing && hasCompanyCode)) && (
				<div className="flex items-center w-fit mt-0 mb-4 ml-1 text-sm text-black border border-gray-200 rounded-lg px-4">
					{codeLoading || sequenceLoading ? (
						<>
							<Skeleton className="h-6 w-8 rounded" />
							<Skeleton className="h-4 w-2 mx-2 rounded" />
							<Skeleton className="h-6 w-12 rounded" />
							<Skeleton className="h-4 w-2 mx-2 rounded" />
							<Skeleton className="h-6 w-12 rounded" />
						</>
					) : (
						<>
							<Button
								variant="ghost"
								onClick={() => handleFilterChange("all")}
								className={cn(
									"rounded-none px-2.3 py-1 shadow-none hover:bg-transparent focus:bg-transparent active:bg-transparent",
									activeFilter === "all"
										? "text-black dark:text-white"
										: "text-gray-600 font-normal",
									!companyCode &&
										!isEditing &&
										"opacity-50 cursor-not-allowed",
								)}
								disabled={!companyCode && !isEditing}
							>
								全部
							</Button>
							<span className="mx-2 select-none text-gray-400">
								|
							</span>
							<Button
								variant="ghost"
								onClick={() => handleFilterChange("starred")}
								className={cn(
									"rounded-none px-2.3 py-1 shadow-none hover:bg-transparent focus:bg-transparent active:bg-transparent",
									activeFilter === "starred"
										? "text-black dark:text-white"
										: "text-gray-600 font-normal",
									!companyCode &&
										!isEditing &&
										"opacity-50 cursor-not-allowed",
								)}
								disabled={!companyCode && !isEditing}
							>
								已收藏
							</Button>
							<span className="mx-2 select-none text-gray-400">
								|
							</span>
							<Button
								variant="ghost"
								onClick={() => handleFilterChange("unstarred")}
								className={cn(
									"rounded-none px-2.3 py-1 shadow-none hover:bg-transparent focus:bg-transparent active:bg-transparent",
									activeFilter === "unstarred"
										? "text-black dark:text-white"
										: "text-gray-600 font-normal",
									!companyCode &&
										!isEditing &&
										"opacity-50 cursor-not-allowed",
								)}
								disabled={!companyCode && !isEditing}
							>
								未收藏
							</Button>
						</>
					)}
				</div>
			)}

			{renderContent()}
		</>
	);
}