"use client";

import { Progress } from "@ui/components/progress";
import { Loader2Icon, CheckCircleIcon, AlertCircleIcon } from "lucide-react";
import { FileProcessResult } from "../hooks/useFileUpload";

interface ProgressDisplayProps {
  // 上传进度相关
  isUploading: boolean;
  uploadProgress: number;
  
  // 解析进度相关
  isParsing: boolean;
  currentParsingFile: string;
  
  // 处理结果相关
  parsedData: FileProcessResult[];
}

/**
 * 进度显示组件
 */
export function ProgressDisplay({
  isUploading,
  uploadProgress,
  isParsing,
  currentParsingFile,
  parsedData,
}: ProgressDisplayProps) {
  return (
    <div className="space-y-4">
      {/* 上传进度 */}
      {isUploading && (
        <div>
          <div className="mb-1 flex justify-between text-xs">
            <span>上传进度</span>
            <span>{uploadProgress}%</span>
          </div>
          <Progress
            value={uploadProgress}
            className="h-2 w-full"
          />
        </div>
      )}

      {/* 文件解析进度 */}
      {isParsing && (
        <div>
          <div className="mb-1 flex justify-center text-lg font-bold">
            <span>
              {currentParsingFile === "批量发送数据中..."
                ? <div className="flex items-center gap-1">
                  </div>
                : "解析文件中..."
              }
            </span>
          </div>
        </div>
      )}

      {/* 解析结果显示 */}
      {/* {parsedData.length > 0 && !isParsing && (
        <div>
          <div className="mb-2 text-sm font-medium">批量处理结果：</div>
          <div className="space-y-1 max-h-32 overflow-y-auto">
            {parsedData.map((result, index) => (
              <div key={index} className="flex items-center justify-between text-xs p-2 rounded bg-muted/30">
                <span className="truncate flex-1">{result.fileName}</span>
                <div className="flex items-center gap-1">
                  {result.success ? (
                    result.skipped ? (
                      <span className="text-yellow-600 text-xs">已跳过</span>
                    ) : result.sent ? (
                      <span className="text-green-600 text-xs">已发送</span>
                    ) : (
                      <CheckCircleIcon className="h-3 w-3 text-green-500" />
                    )
                  ) : (
                    <div className="flex items-center gap-1">
                      <AlertCircleIcon className="h-3 w-3 text-red-500" />
                      <span className="text-red-600 text-xs" title={result.error}>失败</span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )} */}
    </div>
  );
}
