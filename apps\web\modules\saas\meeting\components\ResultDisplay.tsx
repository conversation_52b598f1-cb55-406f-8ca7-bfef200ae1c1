"use client";

import { But<PERSON> } from "@ui/components/button";
import { CardContent } from "@ui/components/card";
import { UploadCloudIcon, FileTextIcon, CopyIcon, DownloadIcon } from "lucide-react";
import { ApiResponse, extractOutputData } from "../utils/meetingFileService";
import { MarkdownRenderer } from "./MarkdownRenderer";

interface ResultDisplayProps {
  apiResult: ApiResponse | null;
  processedFileCount: number;
  meetingTitle: string;
  onResetToUpload: () => void;
}

/**
 * 结果展示组件
 */
export function ResultDisplay({
  apiResult,
  processedFileCount,
  meetingTitle,
  onResetToUpload,
}: ResultDisplayProps) {
  // 复制AI内容到剪贴板
  const handleCopyContent = async () => {
    const outputData = extractOutputData(apiResult);
    try {
      await navigator.clipboard.writeText(String(outputData));
      const { toast } = await import('sonner');
      toast.success("AI内容已复制到剪贴板");
    } catch (error) {
      console.error('复制失败:', error);
      const { toast } = await import('sonner');
      toast.error("复制失败，请手动选择文本复制");
    }
  };

  // 下载AI内容
  const handleDownloadContent = () => {
    const outputData = extractOutputData(apiResult);
    const blob = new Blob([String(outputData)], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${meetingTitle}-AI会前速览.txt`;
    document.body.appendChild(a);
    a.click();
    setTimeout(() => {
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }, 0);
  };

  return (
    <CardContent className="flex-1 flex flex-col">
      {apiResult && (
        <div className="space-y-4 flex-1 overflow-hidden h-[300px]">
          {/* AI分析结果 - 只显示output内容 */}
          <div className="bg-muted/30 rounded-lg p-4 relative">
            <h3 className="font-medium text-sm mb-3 flex items-center gap-2">
              <FileTextIcon className="h-4 w-4 text-blue-500" />
              AI分析结果
            </h3>
            
            <div className="bg-background rounded border p-3 max-h-80 overflow-y-auto">
              
              {(() => {
                const outputData = extractOutputData(apiResult);

                if (typeof outputData === 'string') {
                  // 检查是否是Markdown格式的内容
                  const isMarkdown = outputData.includes('###') || outputData.includes('##') || outputData.includes('- **');

                  if (isMarkdown) {
                    // 使用Markdown渲染器
                    return <MarkdownRenderer content={outputData} />;
                  } else {
                    // 普通文本，直接显示
                    return (
                      <div className="text-sm whitespace-pre-wrap">
                        {outputData}
                      </div>
                    );
                  }
                } else if (typeof outputData === 'object' && outputData !== null) {
                  // 如果output是对象，格式化显示
                  return (
                    <div className="space-y-2">
                      {Object.entries(outputData).map(([key, value]) => (
                        <div key={key} className="border-b border-muted pb-2 last:border-b-0">
                          <div className="font-medium text-xs text-muted-foreground uppercase tracking-wide mb-1">
                            {key}
                          </div>
                          <div className="text-sm">
                            {typeof value === 'object' ? (
                              <pre className="text-xs text-muted-foreground whitespace-pre-wrap">
                                {JSON.stringify(value, null, 2)}
                              </pre>
                            ) : typeof value === 'string' && (value.includes('###') || value.includes('##') || value.includes('- **')) ? (
                              <MarkdownRenderer content={String(value)} />
                            ) : (
                              <span className="whitespace-pre-wrap">{String(value)}</span>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  );
                } else {
                  // 如果没有有效的output数据，显示提示
                  return (
                    <div className="text-sm text-muted-foreground text-center py-4">
                      暂无分析结果
                    </div>
                  );
                }
              })()}
            </div>
            <div className="justify-end flex gap-2">
              <button
                type="button"
                onClick={handleCopyContent}
                title="复制速览报告"
                className="flex items-center gap-1 text-muted-foreground hover:text-foreground rounded p-2"
              >
                <CopyIcon className="h-4 w-4" />
              </button>
              <button 
              type="button" 
              onClick={handleDownloadContent} 
              className="flex items-center gap-1 text-muted-foreground hover:text-foreground rounded p-2"
              title="下载速览报告">
                <DownloadIcon className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      )}
    </CardContent>
  );
}
