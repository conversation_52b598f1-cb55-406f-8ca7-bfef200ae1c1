/**
 * 序列数据管理工具库
 *
 * @fileoverview 提供基金序列数据和基金经理数据的获取功能，支持数据格式化和类型定义
 * <AUTHOR>
 * @since 2025-07-22
 * @version 1.0.0
 *
 * 功能特性：
 * - 基金序列数据查询
 * - 基金经理数据查询
 * - 日期格式化工具
 * - 数据加密传输
 * - 类型安全的接口定义
 * - 错误处理和验证
 */

import {
	encryptRequestData,
	generateSign,
	decryptData,
} from "@repo/utils/lib/crypto";

/**
 * 格式化日期为中文年月日格式
 *
 * @param dateString - 日期字符串（YYYY-MM-DD格式）
 * @returns 格式化后的日期字符串（YYYY年MM月DD日）
 *
 */
export function formatReportDate(dateString: string): string {
	if (!dateString) {
		return '';
	}

	try {
		const date = new Date(dateString);

		// 检查日期是否有效
		if (Number.isNaN(date.getTime())) {
			console.warn('无效的日期格式:', dateString);
			return dateString;
		}

		const year = date.getFullYear();
		const month = date.getMonth() + 1;
		const day = date.getDate();

		return `${year}年${month.toString().padStart(2, '0')}月${day.toString().padStart(2, '0')}日`;
	} catch (error) {
		console.warn('日期格式化失败:', dateString, error);
		return dateString; // 如果格式化失败，返回原始字符串
	}
}

/**
 * 投资人基金持股数据查询请求参数
 * @property {string} organizationId - 组织ID，必填
 */

export interface SequenceParams {
	organizationId: string;
}

/**
 * 标签元数据类型
 * @property {string} reportDate - 报告日期（YYYY-MM-DD格式）
 */
export interface TagMetadata {
	reportDate: string;
}

/**
 * 投资人基金持股数据查询响应单项类型
 * @property {string} id - 主键ID
 * @property {string} companyFilterId - 公司筛选配置ID
 * @property {string} investorCode - 基金/投资人代码
 * @property {string} tagName - 标签名（如“持有本司(000002.SZ)”）
 * @property {string} tagCategory - 标签类型（如“system”/“user”）
 * @property {TagMetadata | null} tagMetadata - 标签元数据，包含报告日期等信息
 * @property {string} modifiedAt - 修改时间（ISO 8601 字符串）
 */
export interface SequenceItem {
	id: string;
	companyFilterId: string;
	investorCode: string;
	tagName: string;
	tagCategory: string;
	tagMetadata: TagMetadata | null;
	modifiedAt: string;
}

/**
 * 投资人基金持股数据查询响应类型
 * @description 返回 SequenceItem 数组
 */
export type SequenceResponse = SequenceItem[];

/**
 * 投资人基金持股数据查询异步函数
 * @param {SequenceParams} params - 查询参数，包含 organizationId
 * @returns {Promise<SequenceResponse>} 基金持股数据数组，包含 investorCode 等信息
 * @throws {Error} 请求或解密失败时抛出
 * @description
 * - 参数会被加密并签名
 * - 响应数据会被解密
 * - 仅处理200响应，其他状态抛出异常
 * - 错误码需由调用方处理
 *
 * 修改时间: 2025-07-12
 * 修改人: Miya
 */
export async function fetchSequenceData(
	params: SequenceParams,
): Promise<SequenceResponse> {
	// 1. 参数加密
	const encrypted = await encryptRequestData(params);

	// 2. 生成签名（使用加密后的内容）
	const sign = generateSign(encrypted);

	// 3. 发送POST请求
	const requestBody = { content: encrypted, sign };

	const res = await fetch(`/api/n8n_proxy/fund_inquiry?organizationId=${params.organizationId}`, {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
			"X-Sign": sign,
		},
		body: JSON.stringify(requestBody),
	});

	if (!res.ok) {
		const errorText = await res.text();
		console.error("fetchSequenceData 请求失败:", res.status, errorText);

		// 502 错误表示没有找到股票代码数据，返回空数组而不是抛出错误
		if (res.status === 502) {
			return [];
		}

		throw new Error(`请求失败: ${res.status} - ${errorText}`);
	}

	// 4. 解密响应（需先 parse 再断言类型）
	const result = await res.json();

	if (result.code !== 200) {
		// 如果是 502 相关的错误码，也返回空数组
		if (result.code === 502) {
			return [];
		}
		throw new Error(result.message || "接口返回错误");
	}
	// decryptData 返回 string，需 JSON.parse
	const data = JSON.parse(decryptData(result.data)) as SequenceResponse;
	// console.log("fetchSequenceData data", data);
	// 注释调试数据
	return data;
}

/**
 * 基金经理数据查询请求参数
 * @property {string[]} investors - 基金代码数组，必填，6位数字+点+字母格式
 */
export interface FundManagerParams {
	investors: string[];
}

/**
 * 基金经理数据查询响应单项类型
 * @property {string} fundCode - 基金代码
 * @property {string} fundName - 基金名称
 * @property {string} nav - 单位净值
 * @property {string} totalAssets - 总资产
 * @property {string} manager - 基金经理
 * @property {string} company - 基金公司
 * @property {string} establishDate - 成立日期
 * @property {string} benchmark - 基准描述
 * @property {string} benchmarkCodes - 基准代码（逗号分隔）
 * @property {string} benchmarkNames - 基准名称（逗号分隔）
 * @property {string} benchmarkTypes - 基准类型（逗号分隔）
 * @property {string} fundType - 基金类型
 * @property {string} id - 唯一ID
 * @property {string} processedAt - 处理时间（ISO字符串）
 */
export interface FundManagerFundItem {
	fundCode: string;
	fundName: string;
	nav: string;
	totalAssets: string;
	manager: string;
	company: string;
	establishDate: string;
	benchmark: string;
	benchmarkCodes: string;
	benchmarkNames: string;
	benchmarkTypes: string;
	fundType: string;
	id: string;
	processedAt: string;
}

/**
 * 基金经理数据查询响应类型
 * @property {FundManagerFundItem[]} funds - 基金列表
 * @property {number} totalCount - 总数
 * @property {string} processedAt - 处理时间（ISO字符串）
 */
export interface FundManagerResponse {
	funds: FundManagerFundItem[];
	totalCount: number;
	processedAt: string;
}

/**
 * 基金经理数据查询异步函数
 * @param {FundManagerParams} params - 查询参数
 * @returns {Promise<FundManagerResponse>} 基金经理数据
 * @throws {Error} 请求或解密失败时抛出
 * @description
 * - 参数会被加密并签名
 * - 响应数据会被解密
 * - 仅处理200响应，其他状态抛出异常
 * - 错误码需由调用方处理
 *
 * 修改时间: 2025-07-11
 * 修改人: Miya
 */
export async function fetchFundManagerData(
	params: FundManagerParams,
): Promise<FundManagerResponse> {
	// 1. 参数加密
	const encrypted = await encryptRequestData(params);

	// 2. 生成签名（使用加密后的内容）
	const sign = generateSign(encrypted);

	// 3. 发送POST请求
	const requestBody = { content: encrypted, sign };

	const res = await fetch("/api/n8n_proxy/fund_manager", {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
			"X-Sign": sign,
		},
		body: JSON.stringify(requestBody),
	});

	if (!res.ok) {
		const errorText = await res.text();
		throw new Error(`请求失败: ${res.status} - ${errorText}`);
	}

	// 4. 解密响应（需先 parse 再断言类型）
	const result = await res.json();
//bug:错误扑捉
	if (result.code !== 200) {
		throw new Error(result.message || "接口返回错误");
	}
	// decryptData 返回 string，需 JSON.parse
	const data = JSON.parse(decryptData(result.data)) as FundManagerResponse;
	// console.log("fetchFundManagerData data", data);
	//注释调试数据
	return data;
}

