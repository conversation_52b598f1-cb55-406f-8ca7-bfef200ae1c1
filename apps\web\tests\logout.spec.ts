import { expect, test } from "@playwright/test";
import { testEmail, testPassword } from "./config";

test.describe("logout functionality", () => {
  test("should successfully logout and redirect to login page", async ({ page }) => {
    // 测试账号信息
    // const testEmail = "<EMAIL>";
    // const testPassword = "9EzTgeOEQ_2XIPNT";
    
    // 从登录页面开始
    await page.goto("/auth/login");
    
    // 登录
    await page.locator('input[autocomplete="email"]').fill(testEmail);
    await page.locator('input[autocomplete="current-password"]').fill(testPassword);
    await page.locator('button[type="submit"]').click();
    
    // 等待重定向到应用页面
    await page.waitForURL(/\/app(\/.*)?$/, { timeout: 10000 });
    
    // 验证已成功登录
    const currentUrl = page.url();
    expect(currentUrl).toMatch(/\/app(\/.*)?$/);
    
    // 处理可能出现的cookie同意弹窗
    try {
      const cookieButton = page.locator('button', { hasText: 'Allow' }).first();
      const isVisible = await cookieButton.isVisible({ timeout: 2000 });
      if (isVisible) {
        await cookieButton.click();
      }
    } catch (e) {
      // 如果弹窗不存在或已关闭，继续测试
      console.log('Cookie banner not found or already closed');
    }
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle');
    
    // 查找并点击用户菜单按钮 - 使用aria-label属性
    // 注意：根据UserMenu.tsx，按钮有aria-label="User menu"属性
    try {
      await page.getByRole('button', { name: 'User menu' }).click({ timeout: 5000 });
      console.log('Clicked user menu by aria-label');
    } catch (e) {
      console.log('Failed to click user menu by aria-label, trying alternative methods');
      
      // 尝试通过测试ID查找
      try {
        // 确保按钮可见
        await page.waitForSelector('[data-testid="user-menu-button"]', { state: 'visible', timeout: 5000 });
        await page.getByTestId('user-menu-button').click({ force: true });
        console.log('Clicked user menu by test ID with force option');
      } catch (e) {
        console.log('Failed to click user menu by test ID, trying JavaScript');
        
        // 使用JavaScript直接点击
        await page.evaluate(() => {
          const button = document.querySelector('[data-testid="user-menu-button"]');
          if (button) (button as HTMLElement).click();
          console.log('Clicked user menu via JavaScript');
        });
      }
    }
    
    // 等待下拉菜单出现
    await page.waitForTimeout(1000);
    
    // 查找并点击退出登录按钮 - 使用测试ID
    try {
      await page.getByTestId('logout-button').click({ timeout: 5000 });
      console.log('Clicked logout button by test ID');
    } catch (e) {
      console.log('Failed to click logout button by test ID, trying alternative methods');
      
      // 尝试通过文本内容查找
      try {
        // 根据UserMenu.tsx，退出按钮文本是t("app.userMenu.logout")
        await page.getByRole('menuitem', { name: /logout|sign out|退出|登出/i }).click({ timeout: 3000 });
        console.log('Clicked logout button by text content');
      } catch (e) {
        console.log('Failed to click logout button by text content, trying JavaScript');
        
        // 使用JavaScript直接点击
        await page.evaluate(() => {
          const button = document.querySelector('[data-testid="logout-button"]');
          if (button) (button as HTMLElement).click();
          console.log('Clicked logout button via JavaScript');
        });
      }
    }
    
    // 等待重定向到登录页面
    await page.waitForURL(/\/auth\/login/, { timeout: 10000 });
    
    // 验证已成功退出登录并重定向到登录页面
    expect(page.url()).toContain("/auth/login");
  });
});







