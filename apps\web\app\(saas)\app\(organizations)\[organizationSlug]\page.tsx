import { getActiveOrganization } from "@saas/auth/lib/server";
import { notFound, redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";

/**
 * 组织首页组件
 * @description 组织的入口页面，负责验证组织有效性并重定向到市值管理页面
 * 该组件作为组织的默认首页，会自动将用户重定向到具体的功能页面
 *
 * @param {Object} props - 组件属性
 * @param {Promise<{organizationSlug: string}>} props.params - 路由参数对象
 * @param {string} props.params.organizationSlug - 组织的URL标识符，用于识别特定组织
 *
 * @returns {Promise<never>} 该函数不返回JSX，而是执行重定向操作
 *
 * @throws {notFound} 当指定的组织不存在或用户无权限访问时，返回404页面
 * @throws {redirect} 当组织验证成功时，重定向到市值管理页面
 *
 * @example
 * // URL访问示例：
 * // /app/my-company -> 重定向到 -> /app/my-company/market
 * // /app/invalid-org -> 返回404页面
 *
 * <AUTHOR>
 * @date 2025/7/22
 * @version 1.0.0
 *
 * @history
 * - 原版本可能直接渲染组织起始页面内容
 * - 当前版本采用重定向策略，将用户导向市值管理功能
 * - 保持了组织验证逻辑，确保安全性
 *
 * @see {@link ./market/page.tsx} 重定向目标页面
 * @see {@link @saas/auth/lib/server.getActiveOrganization} 组织验证函数
 */
export default async function OrganizationPage({
	params,
}: { params: Promise<{ organizationSlug: string }> }) {
	// 解构获取组织标识符参数
	const { organizationSlug } = await params;

	// 获取国际化翻译函数（为未来可能的错误消息做准备）
	const t = await getTranslations();

	// 根据组织标识符获取当前活跃的组织信息
	// 此操作会验证组织是否存在以及用户是否有访问权限
	const activeOrganization = await getActiveOrganization(
		organizationSlug as string,
	);

	// 组织验证失败处理
	// 如果找不到对应的组织或用户无权限访问，返回404页面
	if (!activeOrganization) {
		return notFound();
	}

	// 组织验证成功，执行重定向操作
	// 将用户重定向到该组织的市值管理页面
	// 这样可以确保用户直接访问到核心功能页面
	redirect(`/app/${organizationSlug}/market`);
}
