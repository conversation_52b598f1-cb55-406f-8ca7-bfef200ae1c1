"use client";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@ui/components/select";
import type { Control } from "react-hook-form";
import type { MeetingFormValues } from "../utils/meetingFormValidation";

interface MeetingBasicInfoProps {
  control: Control<MeetingFormValues>;
}

/**
 * 会议基本信息组件
 * 包含会议标题、日期、时间、地点、时区等基本信息字段
 */
export function MeetingBasicInfo({ control }: MeetingBasicInfoProps) {
  return (
    <div className="space-y-6">
      {/* 会议标题 */}
      <FormField
        control={control}
        name="title"
        render={({ field }) => (
          <FormItem className="max-w-[400px]">
            <FormLabel className="text-sm font-medium">
              会议标题
            </FormLabel>
            <FormControl>
              <Input
                placeholder="输入会议标题"
                className="text-xs"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="grid grid-cols-3 gap-x-4 max-w-[400px]">
        {/* 会议日期 */}
        <FormField
          control={control}
          name="date"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium">
                会议日期
              </FormLabel>
              <FormControl>
                <Input
                  type="date"
                  className="h-9 w-full text-xs"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 开始时间 */}
        <FormField
          control={control}
          name="startTime"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium">
                开始时间
              </FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} value={field.value ?? ""}>
                  <SelectTrigger className="h-9 w-full text-xs">
                    <SelectValue placeholder="选择时间" />
                  </SelectTrigger>
                  <SelectContent className="max-h-48 overflow-y-auto">
                    {/* 生成30分钟间隔的时间选项 */}
                    {Array.from({ length: 48 }, (_, i) => {
                      const hour = Math.floor(i / 2);
                      const minute = (i % 2) * 30;
                      const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
                      return (
                        <SelectItem key={timeString} value={timeString}>
                          {timeString}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 结束时间 */}
        <FormField
          control={control}
          name="endTime"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium">
                结束时间
              </FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} value={field.value ?? ""}>
                  <SelectTrigger className="h-9 w-full text-xs">
                    <SelectValue placeholder="选择时间" />
                  </SelectTrigger>
                  <SelectContent className="max-h-48 overflow-y-auto">
                    {/* 生成30分钟间隔的时间选项 */}
                    {Array.from({ length: 48 }, (_, i) => {
                      const hour = Math.floor(i / 2);
                      const minute = (i % 2) * 30;
                      const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
                      return (
                        <SelectItem key={timeString} value={timeString}>
                          {timeString}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* 时区选择 */}
      <FormField
        control={control}
        name="timezone"
        render={({ field }) => (
          <FormItem className="max-w-[400px]">
            <FormLabel className="text-sm font-medium">
              时区
            </FormLabel>
            <Select
              onValueChange={field.onChange}
              value={field.value ?? ""}
            >
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="选择时区" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value="Asia/Shanghai">
                  <p className="text-xs">
                    中国标准时间 (UTC+8)
                  </p>
                </SelectItem>
                <SelectItem value="Asia/Hong_Kong">
                  <p className="text-xs">
                    香港时间 (UTC+8)
                  </p>
                </SelectItem>
                <SelectItem value="Asia/Tokyo">
                  <p className="text-xs">
                    日本标准时间 (UTC+9)
                  </p>
                </SelectItem>
                <SelectItem value="America/New_York">
                  <p className="text-xs">
                    美国东部时间 (UTC-5/4)
                  </p>
                </SelectItem>
                <SelectItem value="America/Los_Angeles">
                  <p className="text-xs">
                    美国太平洋时间 (UTC-8/7)
                  </p>
                </SelectItem>
                <SelectItem value="Europe/London">
                  <p className="text-xs">
                    格林威治标准时间 (UTC+0/1)
                  </p>
                </SelectItem>
                <SelectItem value="Europe/Paris">
                  <p className="text-xs">
                    中欧时间 (UTC+1/2)
                  </p>
                </SelectItem>
                <SelectItem value="Australia/Sydney">
                  <p className="text-xs">
                    澳大利亚东部时间 (UTC+10/11)
                  </p>
                </SelectItem>
              </SelectContent>
            </Select>
            <FormDescription className="text-xs">
              选择会议的时区
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* 会议地点 */}
      <FormField
        control={control}
        name="location"
        render={({ field }) => (
          <FormItem className="max-w-[400px]">
            <FormLabel className="text-sm font-medium">
              会议地点（可选）
            </FormLabel>
            <FormControl>
              <Input
                placeholder="输入会议地点"
                className="text-xs"
                {...field}
              />
            </FormControl>
            <FormDescription className="text-xs">
              可以输入线下会议地点或线上会议平台
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
