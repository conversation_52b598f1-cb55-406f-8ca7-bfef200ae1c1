"use client";

import React, { useState, useCallback, useRef } from "react";
import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";

// 股东数据接口定义
export interface ShareholderData {
  id: string;
  securitiesAccountName: string;
  contactNumber: string;
  registerDate: string;
}

// 带选择状态的股东数据
export interface SelectableShareholderData extends ShareholderData {
  isSelected: boolean;
}

// 分页信息接口
export interface PaginationInfo {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// API响应接口
export interface ShareholdersResponse {
  data: ShareholderData[];
  pagination: PaginationInfo;
}

// Hook选项接口
export interface UseGuestShareholdersOptions {
  enabled?: boolean;
  pageSize?: number;
}

/**
 * 获取嘉宾股东列表钩子
 * 支持分页加载、搜索和数据累积
 * @param organizationId 组织ID
 * @param options Hook配置选项
 * @returns 股东列表查询结果和控制函数
 */
export function useGuestShareholders(
  organizationId: string,
  options: UseGuestShareholdersOptions = {}
) {
  const { enabled = true, pageSize = 20 } = options;

  // 分页状态
  const [page, setPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState<string>("");
  
  // 累积数据状态
  const [accumulatedShareholders, setAccumulatedShareholders] = useState<SelectableShareholderData[]>([]);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // 用于防止重复请求的引用
  const isLoadingRef = useRef(false);

  // 用于跟踪最后处理的页码，避免重复处理
  const lastProcessedPageRef = useRef<number>(0);

  // 查询股东列表
  const query = useQuery<ShareholdersResponse>({
    queryKey: ["guest-shareholders", organizationId, page, pageSize, searchTerm],
    queryFn: async () => {
      if (!organizationId) {
        throw new Error("组织ID不能为空");
      }

      // 构建查询参数
      const params = new URLSearchParams();
      params.append("page", page.toString());
      params.append("pageSize", pageSize.toString());
      if (searchTerm.trim()) {
        params.append("searchTerm", searchTerm.trim());
      }

      const response = await fetch(`/api/meetings/invite-shareholders/${organizationId}?${params}`);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`获取股东数据失败 (${response.status}): ${errorText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || "获取股东数据失败");
      }

      // 处理数据结构
      const shareholdersData = result.data || [];
      const paginationData = result.pagination || {
        page: 1,
        pageSize: pageSize,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      };

      return {
        data: shareholdersData,
        pagination: paginationData
      };
    },
    enabled: enabled && !!organizationId,
    staleTime: 5 * 60 * 1000, // 增加到5分钟，提高缓存效果
    gcTime: 10 * 60 * 1000, // 10分钟后清理缓存
    retry: (failureCount, error) => {
      // 对于网络错误重试，对于业务错误不重试
      if (error.message.includes("组织ID不能为空")) {
        return false;
      }
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  // 处理数据累积逻辑
  const handleDataAccumulation = useCallback((newData: ShareholderData[], currentPage: number) => {

    if (currentPage === 1) {
      // 第一页：直接替换数据，确保搜索结果正确
      const shareholdersWithSelection = newData.map(shareholder => ({
        ...shareholder,
        isSelected: false
      }));
      setAccumulatedShareholders(shareholdersWithSelection);
    } else {
      // 后续页：累积数据，支持无限滚动
      setAccumulatedShareholders(prevData => {
        const existingIds = new Set(prevData.map(item => item.id));
        
        const newItems = newData
          .filter(item => !existingIds.has(item.id))
          .map(shareholder => ({
            ...shareholder,
            isSelected: false
          }));

        const finalData = [...prevData, ...newItems];

        return finalData;
      });
    }
  }, []);

  // 当查询数据变化时，更新累积数据
  React.useEffect(() => {
    if (query.data?.data && lastProcessedPageRef.current !== page) {
      handleDataAccumulation(query.data.data, page);
      lastProcessedPageRef.current = page;
    }
  }, [query.data?.data, page, handleDataAccumulation]);

  // 加载更多数据
  const loadMore = useCallback(async () => {
    if (isLoadingRef.current || !query.data?.pagination?.hasNext || query.isLoading) {
      return;
    }

    isLoadingRef.current = true;
    setIsLoadingMore(true);

    try {
      setPage(prevPage => prevPage + 1);
    } catch (error) {
      console.error("加载更多数据失败:", error);
      toast.error("加载更多数据失败，请重试");
      // 重置加载状态，允许用户重试
      setIsLoadingMore(false);
      isLoadingRef.current = false;
    }
  }, [query.data?.pagination?.hasNext, query.isLoading]);

  // 监听查询状态变化，重置加载状态
  React.useEffect(() => {
    if (!query.isLoading && isLoadingMore) {
      // 延迟重置加载状态，确保API调用完成
      const timer = setTimeout(() => {
        setIsLoadingMore(false);
        isLoadingRef.current = false;
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [query.isLoading, isLoadingMore]);

  // 搜索功能
  const handleSearch = useCallback((newSearchTerm: string) => {
    setSearchTerm(newSearchTerm);
    setPage(1);
    setAccumulatedShareholders([]);
  }, []);

  // 重置搜索
  const resetSearch = useCallback(() => {
    setSearchTerm("");
    setPage(1);
    setAccumulatedShareholders([]);
  }, []);

  // 重置搜索但保留数据（用于模态框关闭时）
  const resetSearchKeepData = useCallback(() => {
    setSearchTerm("");
    setPage(1);
    // 不清空累积数据，保持数据持久化
  }, []);

  // 处理股东选择
  const handleShareholderSelect = useCallback((id: string, isSelected: boolean) => {
    setAccumulatedShareholders(prev =>
      prev.map(shareholder =>
        shareholder.id === id
          ? { ...shareholder, isSelected }
          : shareholder
      )
    );
  }, []);

  // 处理批量选择
  const handleBatchSelect = useCallback((shareholderIds: string[], isSelected: boolean) => {
    setAccumulatedShareholders(prev =>
      prev.map(shareholder =>
        shareholderIds.includes(shareholder.id)
          ? { ...shareholder, isSelected }
          : shareholder
      )
    );
  }, []);

  // 清空所有选择
  const clearAllSelections = useCallback(() => {
    setAccumulatedShareholders(prev =>
      prev.map(shareholder => ({ ...shareholder, isSelected: false }))
    );
  }, []);

  // 获取选中的股东
  const selectedShareholders = accumulatedShareholders.filter(s => s.isSelected);

  return {
    // 数据和状态
    shareholders: accumulatedShareholders,
    pagination: query.data?.pagination,
    isLoading: query.isLoading,
    isLoadingMore,
    error: query.error,
    
    // 搜索相关
    searchTerm,
    
    // 选择相关
    selectedShareholders,
    selectedCount: selectedShareholders.length,
    
    // 控制函数
    loadMore,
    handleSearch,
    resetSearch,
    resetSearchKeepData,
    handleShareholderSelect,
    handleBatchSelect,
    clearAllSelections,

    // 分页控制
    setPage,

    // 刷新数据
    refetch: query.refetch,
  };
}
