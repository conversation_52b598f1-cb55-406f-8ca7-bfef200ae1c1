/**
 * 删除投资人标签路由（仅限用户收藏标签）
 *
 * <AUTHOR>
 * @date 2025-07-09 17:22:38
 * @updated 2025-07-09 17:22:38 hayden 根据投资人管理API实施方案创建标签删除接口
 * @updated 2025-07-15 11:20:39 hayden 修改删除方式从使用id改为使用基金代码
 * @description 删除用户收藏标签（取消收藏功能），系统标签与公司筛选配置挂钩，公司更新时系统标签自动删除
 */
import { Hono } from "hono";
import { db } from "@repo/database";
import { authMiddleware } from "../../../middleware/auth";
import { shareholderCryptoMiddleware } from "../../../middleware/shareholder-crypto";
import { HTTPException } from "hono/http-exception";
import { DeleteInvestorTagSchema } from "../lib/validators";

export const tagsDeleteRouter = new Hono().post(
  "/delete",
  authMiddleware,
  shareholderCryptoMiddleware(),
  async (c) => {
    try {
      const requestData = c.get("requestData");
      // const user = c.get("user"); // 2025-07-15 11:20:39 hayden 注释未使用的变量

      // 参数验证
      const validationResult = DeleteInvestorTagSchema.safeParse(requestData);
      if (!validationResult.success) {
        throw new HTTPException(400, { message: "请求参数无效" });
      }

      // 2025-07-15 11:20:39 hayden 修改从id改为investorCode
      const { organizationId, investorCode } = validationResult.data;

      // 2025-07-15 11:20:39 hayden 修改查找条件从id改为investorCode，并且只查找用户收藏标签
      const existingTag = await db.investorTag.findFirst({
        where: {
          investorCode,
          organizationId,
          tagCategory: "user", // 只查找用户收藏标签
        }
      });

      if (!existingTag) {
        c.set("response", {
          code: 404,
          message: "该基金代码的收藏标签不存在",
          data: null
        });
        return;
      }

      // 2025-07-15 11:20:39 hayden 由于查找条件已经限制为用户标签，此处检查可以简化
      // 仅支持删除用户收藏标签（取消收藏），系统标签通过公司筛选配置联动管理
      // if (existingTag.tagCategory !== "user") {
      //   c.set("response", {
      //     code: 403,
      //     message: "只能删除用户收藏标签，系统标签通过公司筛选配置自动管理",
      //     data: null
      //   });
      //   return;
      // }

      // 2025-07-15 11:20:39 hayden 修改删除条件从id改为使用existingTag的id
      await db.investorTag.delete({
        where: { id: existingTag.id }
      });

      c.set("response", {
        code: 200,
        message: "取消收藏成功",
        data: {
          investorCode: existingTag.investorCode
        }
      });
      return;

    } catch (error) {
      if (error instanceof HTTPException) {
        c.set("response", {
          code: error.status,
          message: error.message,
          data: null
        });
        return;
      }
      
      console.error("投资人管理API错误:", error);
      c.set("response", {
        code: 500,
        message: "服务器内部错误",
        data: null
      });
      return;
    }
  }
);
