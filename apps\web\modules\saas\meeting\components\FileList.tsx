"use client";

import { But<PERSON> } from "@ui/components/button";
import { XIcon, Loader2Icon } from "lucide-react";
import { getFileTypeInfo } from "../utils/fileTypeUtils";

interface FileListProps {
  files: File[];
  onRemoveFile: (index: number) => void;
  onClearFiles: () => void;
  onUpload: () => void;
  isUploading: boolean;
  disabled?: boolean;
}

/**
 * 文件列表组件
 */
export function FileList({
  files,
  onRemoveFile,
  onClearFiles,
  onUpload,
  isUploading,
  disabled = false,
}: FileListProps) {
  if (files.length === 0) {
    return null;
  }

  return (
    <div className="space-y-2">
      {/* 文件列表头部 */}
      <div className="flex items-center justify-between mb-2">
        <div className="font-medium text-sm">
          已选择 {files.length} 个文件
        </div>
        <div className="flex items-center gap-2">
          {/* 上传按钮 */}
          <Button
            onClick={onUpload}
            size="sm"
            disabled={files.length === 0 || isUploading || disabled}
          >
            {isUploading ? (
              <>
                <Loader2Icon className="animate-spin mr-2 h-4 w-4" />
                上传中...
              </>
            ) : (
              <>一键生成</>
            )}
          </Button>
          {/* 清除按钮 */}
          <Button
            variant="outline"
            size="sm"
            onClick={onClearFiles}
            disabled={isUploading || disabled}
          >
            清除全部
          </Button>
        </div>
      </div>

      {/* 文件列表容器 */}
      <div className="rounded-md border-border bg-muted/30 p-2">
        <div className="max-h-[240px] overflow-y-auto pr-1 space-y-2">
          {files.map((file, index) => (
            <div
              key={`${file.name}-${index}`}
              className="rounded-md bg-muted p-3 border border-border/30 hover:border-border/50 transition-colors"
            >
              {/* 文件信息行 */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {getFileTypeInfo(file.name).icon}
                  <div className="min-w-0 flex-1">
                    <div className="font-medium truncate">
                      {file.name}
                    </div>
                    <div className="text-muted-foreground text-xs">
                      {getFileTypeInfo(file.name).text} • {(file.size / 1024 / 1024).toFixed(2)} MB
                    </div>
                  </div>
                </div>
                {/* 删除文件按钮 */}
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 flex-shrink-0"
                  onClick={() => onRemoveFile(index)}
                  disabled={isUploading || disabled}
                >
                  <XIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
