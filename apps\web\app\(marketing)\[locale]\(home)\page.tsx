/**
 * 主页组件
 *
 * @fileoverview 应用程序的营销主页，展示产品特性、FAQ和订阅功能
 * <AUTHOR>
 * @since 2025-01-21
 * @version 1.0.0
 *
 * 功能特性：
 * - 英雄区块展示
 * - 产品特性介绍
 * - 常见问题解答
 * - 邮件订阅功能
 * - 国际化支持
 * - 响应式设计
 *
 * 页面结构：
 * - Hero: 主要宣传区域
 * - Features: 产品特性展示
 * - FaqSection: 常见问题
 * - Newsletter: 邮件订阅
 */

import { FaqSection } from "@marketing/home/<USER>/FaqSection";
import { Features } from "@marketing/home/<USER>/Features";
import { Hero } from "@marketing/home/<USER>/Hero";
import { Newsletter } from "@marketing/home/<USER>/Newsletter";
// import { PricingSection } from "@marketing/home/<USER>/PricingSection";
import { setRequestLocale } from "next-intl/server";

/**
 * 主页组件
 *
 * 渲染应用程序的营销主页，包含英雄区块、特性介绍、FAQ和订阅功能
 *
 * @param params - 页面参数对象
 * @param params.locale - 当前语言环境
 * @returns 主页组件
 *
 * @example
 * ```tsx
 * // 路由: /(marketing)/[locale]/(home)
 * <Home params={{ locale: 'zh' }} />
 * ```
 */
export default async function Home({
	params,
}: {
	params: Promise<{ locale: string }>;
}) {
	const { locale } = await params;
	setRequestLocale(locale);

	return (
		<>
			<Hero />
			<Features />
			{/* <PricingSection /> */}
			<FaqSection />
			<Newsletter />
		</>
	);
}
