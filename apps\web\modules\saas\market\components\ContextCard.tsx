import React, { useState, useEffect, useMemo, useRef } from "react";
import { cn } from "@ui/lib";
import type { SequenceItem, FundManagerFundItem } from "../lib/sequence_data";
import { formatReportDate } from "../lib/sequence_data";
import { InvestorDialog } from "./Dialog";
import { fetchFundDetail } from "../lib/html_dialog";
import { Star } from "lucide-react";
import { useLazyLoadFundData } from "../hooks/useLazyLoadFundData";
import { Skeleton } from "@ui/components/skeleton";
import { useToggleFavorite} from "../hooks/useStar";
import { useSystemScale } from "../../shareholder/hooks/useSystemScale";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
	TooltipPortal
} from "@ui/components/tooltip";

/**
 * ContextCard 组件 - 基金详情卡片和弹窗组件
 * <AUTHOR>
 * @modified 2025-07-03 15:01:58 - 集成 ShadowHtmlRenderer 组件使用 Shadow DOM 完全隔离样式，保持原始布局
 * @modified 2025-07-03 16:08:10 - 替换为 LargeDialog 大容器弹窗组件，支持更大的显示区域和更好的用户体验
 * @modified 2025-07-04 - 增加投资人收藏功能
 * @modified 2025-07-12 - 重构为支持 SequenceResponse 和 FundManagerFundItem 两种数据类型
 */

const PAGE_SIZE = 8; // 每页显示8个卡片，懒加载时每次加载8个数据

// 组合数据类型，包含序列数据和基金详情数据
interface CombinedCardData {
	sequenceItem: SequenceItem;
	fundItem?: FundManagerFundItem;
}

export default function ContextCard({
	sequenceData = [],
	onRefresh,
	organizationId,
	companyFilterId,
	onFavoriteChange,
	favoriteCards: externalFavoriteCards,
	enableFundDataFetch = true,
	fundDataRefreshKey,
}: {
	sequenceData: SequenceItem[];
	onRefresh?: (clearCache?: boolean) => Promise<void> | void;
	organizationId: string;
	companyFilterId: string;
	onFavoriteChange?: (investorCode: string, isFavorited: boolean) => void;
	favoriteCards?: Set<string>;
	enableFundDataFetch?: boolean;
	fundDataRefreshKey?: number;
}) {
	// 使用外部传入的收藏状态，如果没有则使用内部状态
	const [internalFavoriteCards, setInternalFavoriteCards] = useState<Set<string>>(new Set());
	const favoriteCards = externalFavoriteCards || internalFavoriteCards;

	const [open, setOpen] = useState(false);
	const [selectedCard, setSelectedCard] = useState<CombinedCardData | null>(
		null,
	);
	const [html, setHtml] = useState("");
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState("");
	// 新增：分页懒加载状态
	const [visibleCount, setVisibleCount] = useState(PAGE_SIZE);
	const [isScrollLoading, setIsScrollLoading] = useState(false);
	// 添加刷新状态，用于在刷新期间防止显示不一致的数据
	const [isRefreshing, setIsRefreshing] = useState(false);
	// 添加滚动监听器的引用
	const loadMoreTriggerRef = useRef<HTMLDivElement>(null);

	// 收藏功能 hook - 简化版本，只处理 API 调用
	const toggleFavoriteMutation = useToggleFavorite();

	// 使用系统缩放样式配置 - 只用于字体缩放
	const { scale } = useSystemScale();



	// 根据缩放比例计算字体大小
	const getFontSize = (baseSize: string) => {
		if (scale > 1.25) {
			// 高缩放比例时，字体缩小
			switch (baseSize) {
				case 'text-lg': return 'text-base';
				case 'text-sm': return 'text-xs';
				case 'text-xs': return 'text-[10px]';
				default: return baseSize;
			}
		}
		return baseSize;
	};

	// 使用 useMemo 缓存有效的序列数据，避免无限循环
	const validSequenceData = useMemo(() => {
		const result = Array.isArray(sequenceData)
			? sequenceData.filter(item =>
				item != null &&
				typeof item === 'object' &&
				item.investorCode && // 必须有 investorCode
				item.investorCode.trim() !== '' // 确保不是空字符串
			)
			: [];
		return result;
	}, [sequenceData]);

	// 使用懒加载Hook管理基金数据
	const {
		loadedFundData,
		isFundDataLoading,
		currentRequestingCodes,
		resetLoadedData,
		lastLoadedCount,
		setLastLoadedCount
	} = useLazyLoadFundData({
		validSequenceData,
		visibleCount,
		enableFundDataFetch,
		fundDataRefreshKey,
		isRefreshing
	});

	// 当前要渲染的序列数据
	const visibleSequenceItems = validSequenceData.slice(0, visibleCount);

	// 滚动监听，自动加载更多数据
	useEffect(() => {
		const handleScroll = () => {
			// 防抖处理
			if (isScrollLoading) {
				return;
			}

			// 获取多个可能的滚动容器
			const scrollElement = document.documentElement || document.body;
			const scrollTop = window.pageYOffset || scrollElement.scrollTop;
			const windowHeight = window.innerHeight;
			const documentHeight = Math.max(
				document.body.scrollHeight,
				document.body.offsetHeight,
				document.documentElement.clientHeight,
				document.documentElement.scrollHeight,
				document.documentElement.offsetHeight
			);

			// 调试信息（开发环境下可以取消注释）
			// console.log('Scroll Debug:', {
			//   scrollTop,
			//   windowHeight,
			//   documentHeight,
			//   threshold: documentHeight - 200,
			//   shouldTrigger: scrollTop + windowHeight >= documentHeight - 200,
			//   hasMoreData: visibleCount < validSequenceData.length,
			//   visibleCount,
			//   totalData: validSequenceData.length
			// });

			// 滚动到底部前200px时触发加载（增加触发距离）
			if (scrollTop + windowHeight >= documentHeight - 200) {
				// 检查是否还有更多数据可以加载
				if (visibleCount < validSequenceData.length) {
					setIsScrollLoading(true);

					// 延迟加载，模拟网络请求
					setTimeout(() => {
						setVisibleCount(prev => Math.min(prev + PAGE_SIZE, validSequenceData.length));
						setIsScrollLoading(false);
					}, 300);
				}
			}
		};

		// 同时监听 window 和 document 的滚动事件
		window.addEventListener('scroll', handleScroll, { passive: true });
		document.addEventListener('scroll', handleScroll, { passive: true });

		// 添加 resize 事件监听，窗口大小变化时重新计算
		window.addEventListener('resize', handleScroll, { passive: true });

		return () => {
			window.removeEventListener('scroll', handleScroll);
			document.removeEventListener('scroll', handleScroll);
			window.removeEventListener('resize', handleScroll);
		};
	}, [isScrollLoading, visibleCount, validSequenceData.length]);

	// 使用 Intersection Observer 作为备选的懒加载触发机制
	useEffect(() => {
		const triggerElement = loadMoreTriggerRef.current;
		if (!triggerElement) {return;}
		const observer = new IntersectionObserver(
			(entries) => {
				const [entry] = entries;
				if (entry.isIntersecting && !isScrollLoading && visibleCount < validSequenceData.length) {
					setIsScrollLoading(true);
					setTimeout(() => {
						setVisibleCount(prev => Math.min(prev + PAGE_SIZE, validSequenceData.length));
						setIsScrollLoading(false);
					}, 300);
				}
			},
			{
				root: null, // 使用视口作为根
				rootMargin: '100px', // 提前100px触发
				threshold: 0.1
			}
		);

		observer.observe(triggerElement);

		return () => {
			observer.disconnect();
		};
	}, [isScrollLoading, visibleCount, validSequenceData.length]);

	// 完整的刷新处理函数
	const handleDataRefresh = async (clearCache = false) => {
		try {
			setIsRefreshing(true);

			if (clearCache) {
				// 刷新或保存按钮触发：清除基金数据缓存，重置懒加载状态
				setVisibleCount(PAGE_SIZE); // 重置为初始页面大小
				resetLoadedData(); // 清空已加载的基金数据缓存
			} else {
				// 收藏操作触发：只刷新序列数据，保持基金数据缓存和懒加载状态
			}

			// 调用父组件的刷新函数（重新获取序列数据）
			if (onRefresh) {
				await onRefresh(clearCache);
			}
			// 给一个短暂的延迟，确保数据更新完成
			await new Promise(resolve => setTimeout(resolve, 100));
		} catch (error) {
			// 刷新失败，静默处理
		} finally {
			setIsRefreshing(false);
		}
	};





	// 格式化总资产规模函数
	const formatTotalAssets = (totalAssets: string | undefined): string => {
		if (!totalAssets || totalAssets === "-") {
			return "-";
		}

		// 将字符串转换为数字
		const num = Number.parseFloat(totalAssets);
		if (Number.isNaN(num)) {
			return "-";
		}

		// 保留两位小数并添加"亿"单位
		return `${num.toFixed(2)}亿`;
	};

	// 格式化成立时间函数
	const formatEstablishDate = (establishDate: string | undefined): string => {
		if (!establishDate || establishDate === "-") {
			return "-";
		}

		try {
			// 尝试解析日期
			const date = new Date(establishDate);
			if (Number.isNaN(date.getTime())) {
				return establishDate; // 如果无法解析，返回原始值
			}

			// 格式化为年月日
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');

			return `${year}年${month}月${day}日`;
		} catch {
			return establishDate; // 出错时返回原始值
		}
	};





	// 过滤出真正有效的可见序列项（确保不是空对象）并去重
	const validVisibleItems = useMemo(() => {
		const filteredItems = visibleSequenceItems.filter(item =>
			item &&
			typeof item === 'object' &&
			item.investorCode && // 必须有有效的 investorCode
			item.investorCode.trim() !== '' // 确保不是空字符串
		);

		// 按 investorCode 去重，合并标签信息
		const itemMap = new Map<string, SequenceItem>();

		filteredItems.forEach(item => {
			const key = item.investorCode;
			// 由于上面已经过滤了，这里 key 一定存在且有效

			const existing = itemMap.get(key);
			if (!existing) {
				itemMap.set(key, item);
			} else {
				// 如果已存在，合并标签信息，优先保留收藏标签
				const combinedTagName = [existing.tagName, item.tagName]
					.filter(Boolean)
					.join(',');

				// 如果新项目包含"收藏"，则使用新项目作为主要项目
				if (item.tagName?.includes("收藏")) {
					itemMap.set(key, {
						...item,
						tagName: combinedTagName,
					});
				} else {
					// 否则更新现有项目的标签
					itemMap.set(key, {
						...existing,
						tagName: combinedTagName,
					});
				}
			}
		});

		const result = Array.from(itemMap.values());
		return result;
	}, [visibleSequenceItems]);

	// 组合序列数据和累积的基金数据
	// 使用 useMemo 确保在 loadedFundData 更新时重新计算
	const combinedCards: CombinedCardData[] = useMemo(() => {
		return validVisibleItems
			.filter(sequenceItem => {
				// 再次确保数据有效性
				const isValid = sequenceItem?.investorCode &&
					sequenceItem.investorCode.trim() !== '';

				return isValid;
			})
			.map((sequenceItem) => {
				// 从累积的基金数据中查找
				const fundItem = loadedFundData.get(sequenceItem.investorCode);
				return {
					sequenceItem,
					fundItem,
				};
			});
	}, [validVisibleItems, loadedFundData]);

	// 初始化收藏状态 - 只在没有外部收藏状态时才初始化内部状态
	useEffect(() => {
		if (!externalFavoriteCards) {
			const favoriteSet = new Set<string>();
			validSequenceData.forEach((item) => {
				// 只检查用户标签且标签名包含"收藏"的项目
				if (item.tagCategory === "user" && item.tagName?.includes("收藏")) {
					favoriteSet.add(item.investorCode);
				}
			});
			setInternalFavoriteCards(favoriteSet);
		}
	}, [validSequenceData.length, sequenceData, externalFavoriteCards]); // 使用长度和原始数据作为依赖

	// 当序列数据变化时，重置懒加载状态
	// 使用 ref 来跟踪上一次的序列数据，避免收藏操作时的不必要重置
	const prevSequenceDataRef = useRef<SequenceItem[]>([]);
	useEffect(() => {
		// 检查是否是真正的数据变化（不仅仅是收藏状态变化）
		const prevData = prevSequenceDataRef.current;
		const currentData = sequenceData || [];

		// 如果数据长度变化或者 investorCode 集合变化，才重置
		const prevCodes = new Set(prevData.map(item => item.investorCode));
		const currentCodes = new Set(currentData.map(item => item.investorCode));

		const hasStructuralChange =
			prevData.length !== currentData.length ||
			prevCodes.size !== currentCodes.size ||
			Array.from(currentCodes).some(code => !prevCodes.has(code));

		if (hasStructuralChange) {
			resetLoadedData();
			setVisibleCount(PAGE_SIZE);
		}

		// 更新引用
		prevSequenceDataRef.current = currentData;
	}, [sequenceData]); // 只依赖原始的 sequenceData



	// 处理收藏功能 - 简化版本，只调用 API 并直接切换星星状态
	const handleToggleFavorite = async (
		investorCode: string,
		e?: React.MouseEvent,
	) => {
		e?.stopPropagation();

		const isFavorited = favoriteCards.has(investorCode);

		try {
			// 先乐观更新 UI - 立即切换星星状态（只在没有外部状态时更新内部状态）
			if (!externalFavoriteCards) {
				setInternalFavoriteCards(prev => {
					const newSet = new Set(prev);
					if (isFavorited) {
						newSet.delete(investorCode);
					} else {
						newSet.add(investorCode);
					}
					return newSet;
				});
			}

			// 通知父组件更新本地收藏状态，实现卡片立即消失/出现
			if (onFavoriteChange) {
				onFavoriteChange(investorCode, !isFavorited);
			}

			// 调用收藏接口 - 直接传入股票代码，不再需要tagId
			await toggleFavoriteMutation.mutateAsync({
				organizationId,
				companyFilterId,
				investorCode,
				isFavorited,
			});

			// API 调用成功，UI 已经更新，无需额外操作
		} catch (error) {
			// API 调用失败，回滚 UI 状态（只在没有外部状态时回滚内部状态）
			if (!externalFavoriteCards) {
				setInternalFavoriteCards(prev => {
					const newSet = new Set(prev);
					if (isFavorited) {
						// 原本是收藏状态，失败后恢复收藏状态
						newSet.add(investorCode);
					} else {
						// 原本是未收藏状态，失败后恢复未收藏状态
						newSet.delete(investorCode);
					}
					return newSet;
				});
			}

			// 回滚父组件的本地收藏状态
			if (onFavoriteChange) {
				onFavoriteChange(investorCode, isFavorited);
			}

			console.error('收藏操作失败:', error);
		}
	};

	const handleCardClick = async (cardData: CombinedCardData) => {
		setSelectedCard(cardData);
		setOpen(true);
		setLoading(true);
		setError("");
		setHtml("");

		// 使用基金代码获取详情，如果没有基金数据则使用投资人代码
		const codeForDetail =
			cardData.fundItem?.fundCode || cardData.sequenceItem.investorCode;
		const mockCard = {
			code: codeForDetail,
			name: cardData.fundItem?.fundName || cardData.sequenceItem.tagName,
			type: cardData.fundItem?.fundType || "",
			date: cardData.fundItem?.establishDate || "",
			scale: cardData.fundItem?.totalAssets || "",
			yield: cardData.fundItem?.nav || "",
			manager: cardData.fundItem?.manager || "",
			company: cardData.fundItem?.company || "",
			tag: cardData.sequenceItem.tagName,
		};

		const result = await fetchFundDetail(mockCard);
		if (result.html) {
			setHtml(result.html);
		} else {
			setError(result.error || "暂无内容");
		}
		setLoading(false);
	};



	// 渲染内容的条件判断函数
	const renderContent = () => {
		// 早期返回：如果没有序列数据，显示空状态
		if (!sequenceData) {
			return (
				<div className="flex flex-col items-center justify-center h-40 text-gray-400 dark:text-gray-300 text-lg">
					暂无股票数据
				</div>
			);
		}

		// 检查是否是成功响应但数据为空的情况
		if (Array.isArray(sequenceData)) {
			// 检查数组是否有 success 属性（可能是扩展的数组对象）
			const hasSuccessProperty = 'success' in sequenceData;

			if (hasSuccessProperty && (sequenceData as any).success === true && validSequenceData.length === 0) {
				return (
					<div className="flex flex-col items-center justify-center h-40 text-gray-400 dark:text-gray-300 text-lg">
						暂无股票数据
					</div>
				);
			}

			// 如果是普通数组且没有有效数据
			if (validSequenceData.length === 0) {
				return (
					<div className="flex flex-col items-center justify-center h-40 text-gray-400 dark:text-gray-300 text-lg">
						暂无股票数据
					</div>
				);
			}
		}

		// 刷新状态检查：如果正在刷新，显示加载状态
		if (isRefreshing) {
			return (
				<div className="flex flex-col items-center justify-center h-40 text-gray-400 dark:text-gray-300 text-lg">
					正在刷新数据...
				</div>
			);
		}

		// 最终检查：如果没有有效的卡片数据，显示暂无数据
		if (combinedCards.length === 0) {
			return (
				<div className="flex flex-col items-center justify-center h-40 text-gray-400 dark:text-gray-300 text-lg">
					暂无股票数据
				</div>
			);
		}

		// 渲染卡牌区
		return (
		<>
			<div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
				{combinedCards.map((cardData, index) => {
					const { sequenceItem, fundItem } = cardData || {};
					const isCardLoading = !fundItem && currentRequestingCodes.includes(sequenceItem?.investorCode || '');
					if (!sequenceItem) { return null; }
					return (
						<button
							key={
								sequenceItem?.id ||
								sequenceItem?.investorCode ||
								`card-${index}`
							}
							type="button"
							className="rounded-xl shadow bg-white dark:bg-zinc-900 p-6 flex flex-col transition hover:shadow-sm cursor-pointer appearance-none border-0 text-left relative min-h-[320px]"
							onClick={() => handleCardClick(cardData)}
							onKeyUp={(e) => {
								if (e.key === "Enter" || e.key === " ") {
									handleCardClick(cardData);
								}
							}}
						>

							{/* 标题 - 使用flex布局让名称自动适应星星位置 */}
							<div className="mb-2 flex items-start justify-between gap-2">
								<div className="flex-1 min-w-0"> {/* flex-1 让标题占据剩余空间，min-w-0 允许收缩 */}
									{isCardLoading ? (
										<Skeleton className="h-6 w-48" />
									) : (
										(() => {
											const fundName =
												fundItem?.fundName ||
												sequenceItem?.tagName ||
												"-";

											return (
												<TooltipProvider>
													<Tooltip delayDuration={0}>
														<TooltipTrigger asChild>
															<div
																className={cn(
																	"font-bold text-gray-900 dark:text-gray-100 cursor-pointer",
																	"overflow-hidden text-ellipsis whitespace-nowrap block",
																	getFontSize('text-lg')
																)}
															>
																{fundName}
															</div>
														</TooltipTrigger>
														<TooltipPortal>
															<TooltipContent
																side="top"
																align="start"
																className="max-w-[300px]"
															>
																{fundName}
															</TooltipContent>
														</TooltipPortal>
													</Tooltip>
												</TooltipProvider>
											);
										})()
									)}
								</div>
								{/* 星星区域 */}
								<div className="w-7 h-7 flex-shrink-0 flex items-center justify-center">
									<TooltipProvider>
										<Tooltip delayDuration={0}>
											<TooltipTrigger asChild>
												<Star
													className={cn(
														"w-7 h-7",
														(() => {
															if (isCardLoading) {
																return "fill-gray-300 text-gray-300 cursor-not-allowed opacity-50";
															}
															if (
																favoriteCards.has(
																	sequenceItem?.investorCode ||
																		"",
																)
															) {
																return "cursor-pointer";
															}
															return "fill-none cursor-pointer";
														})(),
													)}
													style={
														!isCardLoading
															? favoriteCards.has(
																	sequenceItem?.investorCode || ""
																)
																? { color: "#FFD43B", fill: "#FFD43B" } // 收藏状态：填充
																: { color: "#FFD43B", fill: "none" } // 未收藏状态：只有描边
															: undefined
													}
													onClick={(e) => {
														const investorCode =
															sequenceItem?.investorCode ||
															"";
														if (!isCardLoading) {
															handleToggleFavorite(
																investorCode,
																e,
															);
														} else {
															e.stopPropagation();
														}
													}}
												/>
											</TooltipTrigger>
											<TooltipPortal>
												<TooltipContent
													side="top"
													align="center"
												>
													收藏
												</TooltipContent>
											</TooltipPortal>
										</Tooltip>
									</TooltipProvider>
								</div>
							</div>
							{/* 基金代码 */}
							<div className={cn("text-gray-500 mb-2", getFontSize('text-xs'))}>
								{isCardLoading ? (
									<>
										<Skeleton className="h-3 w-16 inline-block mr-1" />
										<Skeleton className="h-3 w-20 inline-block" />
									</>
								) : (
									<>
										基金代码：
										{sequenceItem?.investorCode || "-"}
									</>
								)}
							</div>
							{/* 主要信息区纵向排列 */}
							<div className="flex flex-col gap-3 mt-1">
								<div className="flex items-center gap-1">
									<span className={cn("font-medium text-gray-700", getFontSize('text-sm'))}>
										{isCardLoading ? (
											<Skeleton className="h-4 w-16" />
										) : (
											"基金类型："
										)}
									</span>
									<span className={cn("text-gray-500", getFontSize('text-sm'))}>
										{isCardLoading ? (
											<Skeleton className="h-4 w-16" />
										) : (
											fundItem?.fundType || "-"
										)}
									</span>
								</div>
								<div className="flex items-center gap-1">
									<span className={cn("font-medium text-gray-700", getFontSize('text-sm'))}>
										{isCardLoading ? (
											<Skeleton className="h-4 w-16" />
										) : (
											"成立时间："
										)}
									</span>
									<span className={cn("text-gray-500", getFontSize('text-sm'))}>
										{isCardLoading ? (
											<Skeleton className="h-4 w-20" />
										) : (
											formatEstablishDate(
												fundItem?.establishDate,
											)
										)}
									</span>
								</div>
								<div className="flex items-center gap-1">
									<span className={cn("font-medium text-gray-700", getFontSize('text-sm'))}>
										{isCardLoading ? (
											<Skeleton className="h-4 w-20" />
										) : (
											"总资产规模："
										)}
									</span>
									<span className={cn("text-gray-500", getFontSize('text-sm'))}>
										{isCardLoading ? (
											<Skeleton className="h-4 w-24" />
										) : (
											formatTotalAssets(
												fundItem?.totalAssets,
											)
										)}
									</span>
								</div>
								<div className="flex items-center gap-1">
									<span className={cn("font-medium text-gray-700", getFontSize('text-sm'))}>
										{isCardLoading ? (
											<Skeleton className="h-4 w-16" />
										) : (
											"基金净值："
										)}
									</span>
									<span
										className={cn(
											getFontSize('text-sm'),
											fundItem?.nav?.startsWith("+")
												? "text-red-500"
												: fundItem?.nav?.startsWith("-")
													? "text-green-500"
													: "text-gray-500 dark:text-gray-100",
										)}
									>
										{isCardLoading ? (
											<Skeleton className="h-4 w-16" />
										) : (
											fundItem?.nav || "-"
										)}
									</span>
								</div>
								<div className="flex items-center gap-1">
									<span className={cn("font-medium text-gray-700", getFontSize('text-sm'))}>
										{isCardLoading ? (
											<Skeleton className="h-4 w-16" />
										) : (
											"基金经理："
										)}
									</span>
									<span className={cn("text-gray-500", getFontSize('text-sm'))}>
										{isCardLoading ? (
											<Skeleton className="h-4 w-20" />
										) : (
											fundItem?.manager || "-"
										)}
									</span>
								</div>
								<div className="flex items-center gap-1">
									<span className={cn("font-medium text-gray-700", getFontSize('text-sm'))}>
										{isCardLoading ? (
											<Skeleton className="h-4 w-16" />
										) : (
											"基金公司："
										)}
									</span>
									<span className={cn("text-gray-500", getFontSize('text-sm'))}>
										{isCardLoading ? (
											<Skeleton className="h-4 w-24" />
										) : (
											fundItem?.company || "-"
										)}
									</span>
								</div>
								{/* 底部标签和时间 */}
								<div className="mt-0 pt-0 pb-1.5 -ml-0.5">
									{/* 时间显示区域 */}
									<div className={cn("text-gray-500 mb-3 ml-0.5 flex items-center", getFontSize('text-sm'))}>
										{isCardLoading ? (
											<>
												<Skeleton className="h-6 w-16 rounded-full" />
												<Skeleton className="h-6 w-16 rounded-full" />
											</>
										) : (
											<>
												<span className={cn("font-medium text-gray-700", getFontSize('text-sm'))}>
													报告时间：
												</span>
												<span className="ml-1">
													{sequenceItem.tagMetadata
														?.reportDate
														? formatReportDate(
																sequenceItem
																	.tagMetadata
																	.reportDate,
															)
														: sequenceItem.modifiedAt
															? formatReportDate(
																	sequenceItem.modifiedAt.split(
																		"T",
																	)[0],
																)
															: "暂无报告时间"}
												</span>
											</>
										)}
									</div>
									{/* 标签区域 */}
									<div className="flex flex-wrap gap-1 mb-1">
										{isCardLoading ? (
											<>
												<Skeleton className="h-6 w-16 rounded-full" />
												<Skeleton className="h-6 w-16 rounded-full" />
											</>
										) : (
											<>
												{sequenceItem.tagName?.includes(
													"持有本司",
												) && (
													<span className={cn("inline-block bg-green-100 text-green-600 px-2 py-0.5 rounded-full mr-1", getFontSize('text-xs'))}>
														{sequenceItem.tagName
															?.split(",")
															.filter((tag) =>
																tag
																	.trim()
																	.includes(
																		"持有本司",
																	),
															)
															.map((tag) =>
																tag.trim(),
															)
															.join(",")}
													</span>
												)}
												{sequenceItem.tagName?.includes(
													"持有对标",
												) && (
													<span className={cn("inline-block bg-orange-100 text-orange-600 px-2 py-0.5 rounded-full", getFontSize('text-xs'))}>
														{sequenceItem.tagName
															?.split(",")
															.filter((tag) =>
																tag
																	.trim()
																	.includes(
																		"持有对标",
																	),
															)
															.map((tag) =>
																tag.trim(),
															)
															.join(",")}
													</span>
												)}
											</>
										)}
									</div>
								</div>
							</div>
						</button>
					);
				})}
			</div>
			{/* 滚动加载提示和触发元素 */}
			<div
				ref={loadMoreTriggerRef}
				className="flex justify-center mt-4 py-4"
			>
				{isScrollLoading ? (
					<div className="text-gray-500 text-sm">正在加载更多...</div>
				) : (
					visibleCount < validSequenceData.length && (
						<div className="h-10 w-full" />
					)
				)}
			</div>
			{/* 弹窗 - 使用 InvestorDialog 组件 */}
			<InvestorDialog
				open={open}
				onOpenChange={setOpen}
				selectedCard={
					selectedCard
						? {
								code:
									selectedCard.fundItem?.fundCode ||
									selectedCard.sequenceItem.investorCode,
								name:
									selectedCard.fundItem?.fundName ||
									selectedCard.sequenceItem.tagName,
								type: selectedCard.fundItem?.fundType || "",
								date:
									selectedCard.fundItem?.establishDate || "",
								scale: selectedCard.fundItem?.totalAssets || "",
								yield: selectedCard.fundItem?.nav || "",
								manager: selectedCard.fundItem?.manager || "",
								company: selectedCard.fundItem?.company || "",
								tag: selectedCard.sequenceItem.tagName,
							}
						: null
				}
				html={html}
				loading={loading}
				error={error}
				isFavorite={
					selectedCard
						? favoriteCards.has(
								selectedCard.sequenceItem.investorCode,
							)
						: false
				}
				onToggleFavorite={() => {
					if (selectedCard) {
						const investorCode = selectedCard.sequenceItem.investorCode;
						handleToggleFavorite(investorCode);
					}
				}}
				onRefresh={handleDataRefresh}
				organizationId={organizationId}
				companyFilterId={companyFilterId}
				investorCode={selectedCard?.sequenceItem.investorCode}
				tagId={selectedCard?.sequenceItem.id}
			/>

			{/* 右下角进度显示 */}
			{validSequenceData.length > 0 && (
				<div className="fixed bottom-4 right-4 bg-white/90 backdrop-blur-sm border border-gray-200 rounded-lg px-3 py-2 shadow-lg text-sm text-gray-600 z-50">
					{Math.min(visibleCount, validSequenceData.length)}/{validSequenceData.length}
				</div>
			)}
		</>
		);
	};

	return renderContent();
}
