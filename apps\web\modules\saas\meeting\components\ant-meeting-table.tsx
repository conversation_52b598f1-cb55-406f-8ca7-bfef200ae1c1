import * as React from "react";
import { cn } from "@ui/lib";
import { Table, Config<PERSON><PERSON><PERSON>, But<PERSON>} from "antd";
import type { TableProps, TableColumnType } from "antd";
import type { SpinProps } from "antd";
import { useTheme } from "next-themes";
import zhCN from "antd/lib/locale/zh_CN";
import { ChevronDown, ChevronUp } from "lucide-react";
import type { Meeting } from "@saas/meeting/components/types";

import Link from "next/link";

// 禁用 antd 的 React 版本兼容性警告
const originalConsoleError = console.error;
console.error = (...args: any[]) => {
	if (
		typeof args[0] === "string" &&
		args[0].includes("antd: compatible") &&
		args[0].includes("antd v5 support React is 16 ~ 18")
	) {
		return;
	}
	originalConsoleError(...args);
};

/**
 * 会议表格项目接口
 */


/**
 * 会议表格列配置接口
 */
export interface MeetingColumn {
	key: string;
	title: string;
	className?: string;
	render?: (value: any, record: Meeting) => React.ReactNode;
	sortable?: boolean;
	width?: number;
	hidden?: boolean;
}

/**
 * 会议表格组件接口
 */
export interface AntMeetingTableProps<T = Meeting> {
	columns: TableColumnType<T>[];
	data: T[];
	className?: string;
	headerClassName?: string;
	rowClassName?: string;
	cellClassName?: string;
	onRowClick?: (item: T) => void;
	emptyContent?: React.ReactNode;
	loading?: boolean | SpinProps;
	onSort?: (sortKey: string, sortOrder: "asc" | "desc") => void;
	footer?: React.ReactNode;
	onScroll?: (event: React.UIEvent<HTMLDivElement>) => void; // 添加滚动事件处理
	scroll?: object; // 添加滚动配置

	// 会议相关的回调函数
	handleCancelMeeting?: (meetingId: string) => void;
	handleExportParticipants?: (meetingId: string, title: string) => void;
	handleGetSignInRecord?: (meetingId: string, title: string) => void;
	handleGetMeetingDocs?: (meetingId: string, title: string) => void;
	handleGetMeetingRecord?: (meetingId: string, title: string) => void;
	handleShowTranscript?: (meetingId: string, title: string) => void;
	handleShowAISummary?: (meetingId: string, title: string) => void;

	// 配置选项
	showCancelOption?: boolean;
	showDocAndRecordButtons?: boolean;
	cancelingMeetingId?: string | null;
	organizationSlug?: string;
	timeColumnLabel?: string;
	sortDirection?: "asc" | "desc";
	toggleSortDirection?: () => void;
}

/**
 * 生成表格的自定义样式
 */
function getTableStyle(isDarkTheme: boolean): React.CSSProperties {
	return {
		"--ant-table-bg": isDarkTheme ? "var(--card)" : "white",
		"--ant-table-header-bg": isDarkTheme ? "var(--card)" : "white",
		"--ant-table-header-color": isDarkTheme
			? "rgba(255, 255, 255, 0.85)"
			: "var(--foreground)",
		"--ant-table-text-color": isDarkTheme
			? "rgba(255, 255, 255, 0.85)"
			: "var(--foreground)",
		"--ant-table-row-hover-bg": isDarkTheme
			? "rgba(255, 255, 255, 0.08)"
			: "rgba(0, 0, 0, 0.04)",
		"--ant-light-row-hover-bg": "rgba(0, 0, 0, 0.04)",
		"--ant-table-text-hover-color": isDarkTheme
			? "#fff"
			: "var(--foreground)",
		"--ant-table-border-color": isDarkTheme
			? "rgba(255, 255, 255, 0.12)"
			: "var(--border)",
		"--ant-scrollbar-thumb-color": isDarkTheme
			? "rgba(255, 255, 255, 0.3)"
			: "rgba(0, 0, 0, 0.2)",
		"--ant-scrollbar-track-color": isDarkTheme
			? "rgba(255, 255, 255, 0.05)"
			: "rgba(0, 0, 0, 0.05)",
	} as React.CSSProperties;
}

/**
 * 会议表格组件
 */
export function AntMeetingTable<T = Meeting>({
	columns,
	data,
	className,
	rowClassName,
	onRowClick,
	emptyContent = <div className="h-full" />,
	loading = false,
	onSort,
	footer,
	onScroll, // 添加滚动事件处理
	scroll: scrollProp, // 添加滚动配置，重命名以避免与内部变量冲突

	// 会议相关的回调函数
	handleCancelMeeting,
	handleExportParticipants,
	handleGetSignInRecord,
	handleGetMeetingDocs,
	handleGetMeetingRecord,
	handleShowTranscript,
	handleShowAISummary,

	// 配置选项
	showCancelOption = false,
	showDocAndRecordButtons = false,
	cancelingMeetingId,
	organizationSlug = "",
	timeColumnLabel = "时间",
	sortDirection = "desc",
	toggleSortDirection,
}: AntMeetingTableProps<T>): JSX.Element {
	// 获取当前主题
	const { resolvedTheme } = useTheme();
	const isDarkTheme = resolvedTheme === "dark";

	

	// 处理排序变化
	const handleChange: TableProps<T>["onChange"] = (
		_pagination,
		_filters,
		sorter,
	) => {
		if (sorter && !Array.isArray(sorter) && onSort) {
			const field = sorter.field as string;
			const sortOrder = sorter.order === "ascend" ? "asc" : "desc";
			onSort(field, sortOrder);
		}
	};

	// 生成Ant Design表格的类名
	const tableClassName = cn(
		"ant-meeting-table",
		{
			"ant-meeting-table-dark": isDarkTheme,
		},
		className,
	);

	// 行点击处理
	const onRow = onRowClick
		? (record: T) => ({
				onClick: () => {
					onRowClick(record);
				},
			})
		: undefined;

	// 使用提取的函数获取样式
	const tableStyle = React.useMemo(
		() => getTableStyle(isDarkTheme),
		[isDarkTheme],
	);

	// 滚动配置
	const scroll = React.useMemo(() => {
		// 如果传入了自定义滚动配置，使用传入的配置
		if (scrollProp) {
			return scrollProp;
		}

		// 否则使用默认配置
		const shouldEnableYScroll = data.length > 10;

		return {
			x: "max-content",
			y: shouldEnableYScroll ? 500 : undefined,
			scrollToFirstRowOnChange: true,
		};
	}, [data.length, scrollProp]);

	// 自定义表格文案
	const customLocale = React.useMemo(() => {
		return {
			...zhCN.Table,
			triggerDesc: "点击降序排列",
			triggerAsc: "点击升序排列",
			cancelSort: "取消排序",
			emptyText: emptyContent,
		};
	}, [emptyContent]);

	return (
		<div className={tableClassName} style={tableStyle}>
			<style jsx global>{`
        /* Ant Design表格自定义样式 */
        .ant-meeting-table {
          --scrollbar-size: 8px;
        }
        
        /* 基础样式 */
        .ant-shareholder-table .ant-table {
          background-color: var(--ant-table-bg);
          color: var(--ant-table-text-color);
        }
        
        .ant-meeting-table .ant-table-container {
          border-radius: var(--radius-sm);
          overflow: hidden;
        }
        
        /* 调整表格单元格内边距和行高 */
        .ant-shareholder-table .ant-table-tbody > tr > td {
          padding: 10px 8px;
          height: 48px; /* 固定行高 */
          line-height: 1.4;
          vertical-align: middle;
        }
        
        /* 调整表头内边距和高度 */
        .ant-meeting-table .ant-table-thead > tr > th {
          padding: 12px 8px;
          height: 46px;
          background-color: var(--ant-table-header-bg) !important;
          color: var(--ant-table-header-color);
          border-bottom: none !important;
          font-weight: 500 !important;
          font-family: inherit !important;
          text-align: center;
          line-height: 1.5715;
          vertical-align: middle;
          font-size: 14px;
        }
        
        /* 修复表头字体不一致问题 */
        .ant-meeting-table .ant-table-thead > tr > th .ant-table-column-title {
          font-weight: 500;
          font-family: inherit;
          color: inherit;
          font-size: 14px;
        }
        
        /* 黑色主题下表头颜色特殊处理 */
        .ant-meeting-table-dark .ant-table-thead > tr > th .ant-table-column-title {
          color: rgba(255, 255, 255, 0.9);
        }
        
        .ant-meeting-table .ant-table-tbody > tr > td {
          border-bottom: none !important;
          color: var(--ant-table-text-color);
          font-size: 14px;
          text-align: center;
        }
        
        /* 深色主题下文字颜色增强 */
        .ant-meeting-table-dark .ant-meeting-table .ant-table-tbody > tr > td {
          color: rgba(255, 255, 255, 0.85);
        }
        
        /* 修改于2025-06-12: 强制覆盖暗色主题下的按钮和链接文字颜色，解决text-black-700类优先级问题 */
        /* 原问题: 操作列中的按钮使用text-black-700类，在暗色主题下无法适配 */
        /* 修改范围: 覆盖.ant-meeting-table-dark下所有按钮和链接的文字颜色 */
        /* 恢复方法: 删除下面的CSS规则块，恢复原有样式 */
        .ant-meeting-table-dark .ant-table-tbody > tr > td .ant-btn,
        .ant-meeting-table-dark .ant-table-tbody > tr > td a,
        .ant-meeting-table-dark .ant-table-tbody > tr > td button {
          color: rgba(255, 255, 255, 0.85) !important;
        }
        
        .ant-meeting-table-dark .ant-table-tbody > tr > td .ant-btn:hover,
        .ant-meeting-table-dark .ant-table-tbody > tr > td a:hover,
        .ant-meeting-table-dark .ant-table-tbody > tr > td button:hover {
          color: rgba(255, 255, 255, 1) !important;
        }
        
        /* 特殊处理危险按钮(取消按钮)的颜色 */
        .ant-meeting-table-dark .ant-table-tbody > tr > td .ant-btn.ant-btn-dangerous {
          color: #ff4d4f !important;
        }
        
        .ant-meeting-table-dark .ant-table-tbody > tr > td .ant-btn.ant-btn-dangerous:hover {
          color: #ff7875 !important;
        }
        
        /* 深色主题下链接文字颜色 */
        .ant-meeting-table-dark .ant-table-tbody > tr > td a {
          color: #4b9eff;
        }
        
        /* 深色主题下表格边框 */
        .ant-meeting-table-dark .ant-table {
          border-color: rgba(255, 255, 255, 0.12);
        }
        
        /* 悬停效果 */
        .ant-table-tbody > tr.ant-table-row,
        .ant-table-tbody > tr.ant-table-row > td {
          transition: background-color 0.2s ease-in-out !important;
        }
        
        /* 修复悬停效果在黑暗模式下变白的问题 */
        .ant-meeting-table .ant-table-tbody > tr.ant-table-row:hover > td {
          background-color: transparent !important; 
        }
        
        /* 浅色主题悬停样式 */
        .ant-meeting-table:not(.ant-meeting-table-dark) .ant-table-tbody > tr.ant-table-row:hover > td,
        .ant-meeting-table:not(.ant-meeting-table-dark) .ant-table-row-hover > td,
        .ant-meeting-table:not(.ant-meeting-table-dark) .ant-table-tbody > tr > td.ant-table-cell-row-hover {
          background-color: var(--ant-light-row-hover-bg) !important;
        }
        
        /* 直接覆盖Ant Design的原生悬停样式 */
        .ant-table-wrapper .ant-table-tbody > tr.ant-table-row:hover > td,
        .ant-table-wrapper .ant-table-tbody > tr > td.ant-table-cell-row-hover {
          background-color: var(--ant-table-row-hover-bg) !important;
          transition: background-color 0.2s ease-in-out !important;
        }
        
        /* 空状态样式 */
        .ant-meeting-table .ant-empty-description {
          color: var(--ant-table-text-color);
          font-size: 14px;
        }
        
        /* 排序图标样式 */
        .ant-meeting-table-dark .ant-table-column-sorter-up.active,
        .ant-meeting-table-dark .ant-table-column-sorter-down.active {
          color: #4b9eff;
        }
        
        .ant-meeting-table .ant-table-column-sorter-up.active,
        .ant-meeting-table .ant-table-column-sorter-down.active {
          color: var(--primary, #1890ff);
        }
        
        .ant-meeting-table .ant-table-filter-trigger {
          color: var(--ant-table-header-color);
        }
        
        .ant-meeting-table .ant-table-column-sorter {
          color: var(--ant-table-header-color);
        }
        
        /* 修复排序列背景变黑的问题 */
        .ant-meeting-table .ant-table-tbody > tr > td.ant-table-column-sort {
          background-color: transparent !important;
        }
        
        /* 亮色主题下排序列背景样式 */
        .ant-meeting-table:not(.ant-meeting-table-dark) .ant-table-tbody > tr > td.ant-table-column-sort {
          background-color: rgba(0, 0, 0, 0.02) !important;
        }
        
        /* 暗色主题下排序列背景样式 */
        .ant-meeting-table-dark .ant-table-tbody > tr > td.ant-table-column-sort {
          background-color: rgba(255, 255, 255, 0.04) !important;
        }
        
        /* 确保排序列中的行悬停效果正常 */
        .ant-meeting-table:not(.ant-meeting-table-dark) .ant-table-tbody > tr:hover > td.ant-table-column-sort {
          background-color: var(--ant-light-row-hover-bg) !important;
        }
        
        .ant-meeting-table-dark .ant-table-tbody > tr:hover > td.ant-table-column-sort {
          background-color: var(--ant-table-row-hover-bg) !important;
        }
        
        /* 自定义滚动条样式 */
        .ant-meeting-table .ant-table-body::-webkit-scrollbar,
        .ant-meeting-table .ant-table-header::-webkit-scrollbar {
          width: var(--scrollbar-size);
          height: var(--scrollbar-size);
        }
        
        .ant-meeting-table .ant-table-body::-webkit-scrollbar-thumb,
        .ant-meeting-table .ant-table-header::-webkit-scrollbar-thumb {
          background-color: var(--ant-scrollbar-thumb-color);
          border-radius: calc(var(--scrollbar-size) / 2);
        }
        
        .ant-meeting-table .ant-table-body::-webkit-scrollbar-track,
        .ant-meeting-table .ant-table-header::-webkit-scrollbar-track {
          background-color: var(--ant-scrollbar-track-color);
        }
        
        /* 适配Firefox滚动条 */
        .ant-meeting-table .ant-table-body,
        .ant-meeting-table .ant-table-header {
          scrollbar-width: thin;
          scrollbar-color: var(--ant-scrollbar-thumb-color) var(--ant-scrollbar-track-color);
        }
        
        /* 修复筛选菜单在暗色模式下的颜色问题 */
        .ant-meeting-table-dark .ant-dropdown-menu {
          background-color: var(--popover);
          color: var(--popover-foreground);
        }
        
        .ant-meeting-table-dark .ant-dropdown-menu-item {
          color: var(--popover-foreground);
          font-size: 14px;
        }
        
        .ant-meeting-table-dark .ant-dropdown-menu-item:hover {
          background-color: var(--accent);
        }
        
        /* 加载状态样式 */
        .ant-meeting-table .ant-spin {
          color: var(--primary);
        }
        
        /* 移除最后一列的垂直分隔线 */
        .ant-meeting-table .ant-table-thead > tr > th:last-child,
        .ant-meeting-table .ant-table-tbody > tr > td:last-child {
          border-right: none !important;
        }
        
        /* 隐藏表格垂直分隔线 */
        .ant-meeting-table .ant-table-tbody > tr > td {
          border-right: none !important;
        }
        
        /* 隐藏右侧滚动指示器 */
        .ant-meeting-table .ant-table-container::after {
          display: none !important;
        }

        /* 按钮样式统一 */
        .ant-meeting-table .ant-btn {
          font-size: 14px;
          height: 32px;
          padding: 4px 15px;
          border-radius: 2px;
        }

        /* 链接样式统一 */
        .ant-meeting-table .ant-table-tbody > tr > td a {
          color: rgba(0, 0, 0, 0.85);
          font-size: 14px;
        }

        .ant-meeting-table .ant-table-tbody > tr > td a:hover {
          color: #1890ff;
        }

        /* 下拉菜单样式统一 */
        .ant-meeting-table .ant-dropdown-menu {
          padding: 4px 0;
          font-size: 14px;
        }

        .ant-meeting-table .ant-dropdown-menu-item {
          padding: 5px 12px;
          line-height: 22px;
        }

        /* 固定列阴影效果 */
        .ant-meeting-table .ant-table-cell-fix-left-last::after,
        .ant-meeting-table .ant-table-cell-fix-right-first::after {
          box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.1) !important;
        }

        /* 自定义Tooltip样式 */
        .ant-tooltip .ant-tooltip-inner {
          background-color: var(--popover);
          color: var(--popover-foreground);
        }

        .ant-tooltip .ant-tooltip-arrow-content {
          background-color: var(--popover);
        }

        /* 确保垂直分隔线不显示 */
        .ant-meeting-table .ant-table-container table > thead > tr:first-child th:last-child {
          border-right: none !important;
        }

        /* 确保表格滚动区域不显示垂直线 */
        .ant-meeting-table .ant-table-ping-right .ant-table-cell-fix-right-first::after,
        .ant-meeting-table .ant-table-ping-right .ant-table-cell-fix-right-last::after {
          box-shadow: none !important;
        }


        /* 或者完全隐藏 */
        .ant-meeting-table .ant-table-cell-scrollbar {
          display: none !important;
        }
      `}</style>

			<ConfigProvider
				theme={{
					hashed: true,
					components: {
						Table: {
							colorBgContainer: isDarkTheme
								? "var(--card)"
								: "white",
							borderRadius: 0,
							colorBorderSecondary: isDarkTheme
								? "rgba(255, 255, 255, 0.12)"
								: "rgba(0, 0, 0, 0.06)",
							colorText: isDarkTheme
								? "rgba(255, 255, 255, 0.85)"
								: "rgba(0, 0, 0, 0.85)",
							colorTextHeading: isDarkTheme
								? "rgba(255, 255, 255, 0.85)"
								: "rgba(0, 0, 0, 0.85)",
							colorTextSecondary: isDarkTheme
								? "rgba(255, 255, 255, 0.65)"
								: "rgba(0, 0, 0, 0.65)",
							colorTextDisabled: isDarkTheme
								? "rgba(255, 255, 255, 0.3)"
								: "rgba(0, 0, 0, 0.3)",
							colorBgTextHover: isDarkTheme
								? "rgba(255, 255, 255, 0.08)"
								: "rgba(0, 0, 0, 0.04)",
							colorFillAlter: isDarkTheme
								? "rgba(255, 255, 255, 0.02)"
								: "rgba(0, 0, 0, 0.02)",
							motionDurationMid: "0s",
							motionDurationSlow: "0s",
							fontSize: 14,
							lineHeight: 1.5715,
						},
						Button: {
							colorPrimary: "#1890ff",
							colorPrimaryHover: "#40a9ff",
							colorPrimaryActive: "#096dd9",
							borderRadius: 2,
							fontSize: 14,
							lineHeight: 1.5715,
						},
						Dropdown: {
							colorBgElevated: isDarkTheme
								? "var(--popover)"
								: "white",
							colorText: isDarkTheme
								? "rgba(255, 255, 255, 0.85)"
								: "rgba(0, 0, 0, 0.85)",
							borderRadius: 2,
						},
					},
				}}
			>
				<Table
					dataSource={data}
					columns={columns}
					rowKey="id"
					loading={loading}
					onChange={handleChange}
					onRow={onRow}
					pagination={false}
					scroll={scroll}
					locale={customLocale}
						onScroll={onScroll} // 添加滚动事件处理
					rowClassName={(_, index) =>
						cn(
							// 修改于2025年06月12日: 调整行背景颜色，使单数行高亮显示
							// 原代码: index % 2 === 1 ? (isDarkTheme ? "bg-gray-700/20" : "bg-slate-100/60") : "",
							index % 2 === 0
								? isDarkTheme
									? "bg-gray-700/20"
									: "bg-slate-100/60"
								: "",
							rowClassName,
						)
					}
					footer={footer ? () => footer : undefined}
					size="middle"
					bordered={false}
				/>
			</ConfigProvider>
		</div>
	);
}
