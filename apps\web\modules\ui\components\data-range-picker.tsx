/**
 * 日期范围选择器组件
 *
 * @fileoverview 基于Ant Design的日期范围选择器组件，支持多种预设和自定义配置
 * <AUTHOR>
 * @since 2025-07-22
 * @version 1.0.0
 *
 * 功能特性：
 * - 基于Ant Design DatePicker.RangePicker
 * - 中文本地化支持
 * - 丰富的预设日期范围
 * - 多种选择器类型（日期、周、月、季度、年）
 * - 时间选择支持
 * - 自定义格式化
 * - 响应式设计
 * - 类型安全的接口
 */

"use client";

import * as React from "react";
import { DatePicker, ConfigProvider } from "antd";
import type { RangePickerProps } from "antd/es/date-picker";
import type { Dayjs } from "dayjs";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import zhCN from "antd/locale/zh_CN";
import { cn } from "@ui/lib";

// 设置dayjs中文语言
dayjs.locale("zh-cn");

const { RangePicker } = DatePicker;

/**
 * 日期范围类型定义
 *
 * 支持null值表示未选择状态
 */
export type DateRange = [Dayjs | null, Dayjs | null] | null;

/**
 * 预设日期范围配置接口
 */
export interface PresetRange {
  /** 预设选项的显示标签 */
  label: string;
  /** 预设的日期范围值 */
  value: DateRange;
}

/**
 * 日期范围选择器组件属性接口
 */
export interface DateRangePickerProps
  extends Omit<RangePickerProps, 'value' | 'onChange' | 'presets'> {
  /** 当前选中的日期范围 */
  value?: DateRange;
  /** 日期范围变化回调函数 */
  onChange?: (dates: DateRange, dateStrings: [string, string]) => void;
  /** 自定义CSS类名 */
  className?: string;
  /** 自定义预设日期范围 */
  presets?: PresetRange[];
  /** 是否显示预设选项，默认为true */
  showPresets?: boolean;
  /** 选择器类型，默认为"date" */
  picker?: "date" | "week" | "month" | "quarter" | "year";
  /** 是否显示时间选择，默认为false */
  showTime?: boolean | object;
  /** 日期格式字符串 */
  format?: string;
  /** 输入框占位符 */
  placeholder?: [string, string];
  /** 是否禁用选择器 */
  disabled?: boolean | [boolean, boolean];
  /** 选择器尺寸 */
  size?: "large" | "middle" | "small";
  /** 是否允许清除，默认为true */
  allowClear?: boolean;
}

/**
 * 获取默认预设日期范围配置
 *
 * @returns 包含常用日期范围的预设配置数组
 */
const getDefaultPresets = (): PresetRange[] => {
  const today = dayjs();

  return [
    {
      label: "今天",
      value: [today, today]
    },
    {
      label: "昨天",
      value: [today.subtract(1, 'day'), today.subtract(1, 'day')]
    },
    {
      label: "最近7天",
      value: [today.subtract(6, 'day'), today]
    },
    {
      label: "最近30天",
      value: [today.subtract(29, 'day'), today]
    },
    {
      label: "本周",
      value: [today.startOf('week'), today.endOf('week')]
    },
    {
      label: "本月",
      value: [today.startOf('month'), today.endOf('month')]
    },
    {
      label: "本年",
      value: [today.startOf('year'), today.endOf('year')]
    }
  ];
};

/**
 * 日期范围选择器主组件
 *
 * @param props - 组件属性
 * @returns 日期范围选择器组件
 *
 * @example
 * ```tsx
 * const [dateRange, setDateRange] = useState<DateRange>(null);
 *
 * <DateRangePicker
 *   value={dateRange}
 *   onChange={setDateRange}
 *   showPresets={true}
 *   placeholder={["开始日期", "结束日期"]}
 * />
 * ```
 */
export const DateRangePicker: React.FC<DateRangePickerProps> = ({
  value,
  onChange,
  className,
  presets,
  showPresets = true,
  picker = "date",
  showTime = false,
  format,
  placeholder = ["开始日期", "结束日期"],
  disabled = false,
  size = "middle",
  allowClear = true,
  ...restProps
}) => {
  const defaultPresets = React.useMemo(() => getDefaultPresets(), []);
  const finalPresets = presets || defaultPresets;

  // 根据picker类型设置默认格式
  const getDefaultFormat = () => {
    switch (picker) {
      case "week":
        return "YYYY-wo";
      case "month":
        return "YYYY-MM";
      case "quarter":
        return "YYYY-[Q]Q";
      case "year":
        return "YYYY";
      default:
        return showTime ? "YYYY-MM-DD HH:mm:ss" : "YYYY-MM-DD";
    }
  };

  const finalFormat = format || getDefaultFormat();

  // 构建预设选项
  const rangePresets = showPresets
    ? (finalPresets as any).map((preset: PresetRange) => ({
        label: preset.label,
        value: preset.value as [Dayjs, Dayjs]
      }))
    : undefined;

  return (
    <ConfigProvider locale={zhCN}>
      <RangePicker
        value={value ?? undefined}
        onChange={onChange as any}
        className={cn("w-full [&.ant-picker]:flex [&.ant-picker]:flex-row [&.ant-picker]:items-center", className)}
        style={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}
        picker={picker}
        showTime={showTime}
        format={finalFormat}
        placeholder={placeholder}
        disabled={disabled}
        size={size}
        allowClear={allowClear}
        presets={rangePresets}
        allowEmpty={[true, true]}
        {...restProps}
      />
    </ConfigProvider>
  );
};

// 导出一些常用的预设配置
export const commonPresets = {
  // 基础日期预设
  basic: getDefaultPresets(),

  // 业务相关预设
  business: [
    {
      label: "本周工作日",
      value: [
        dayjs().startOf('week').add(1, 'day'), // 周一
        dayjs().startOf('week').add(5, 'day')  // 周五
      ] as DateRange
    },
    {
      label: "上周",
      value: [
        dayjs().subtract(1, 'week').startOf('week'),
        dayjs().subtract(1, 'week').endOf('week')
      ] as DateRange
    },
    {
      label: "上月",
      value: [
        dayjs().subtract(1, 'month').startOf('month'),
        dayjs().subtract(1, 'month').endOf('month')
      ] as DateRange
    }
  ],

  // 财务相关预设
  financial: [
    {
      label: "本财年",
      value: [
        dayjs().month(3).startOf('month'), // 4月1日
        dayjs().add(1, 'year').month(2).endOf('month') // 次年3月31日
      ] as DateRange
    },
    {
      label: "上财年",
      value: [
        dayjs().subtract(1, 'year').month(3).startOf('month'),
        dayjs().month(2).endOf('month')
      ] as DateRange
    }
  ]
};

// 工具函数：格式化日期范围为字符串
export const formatDateRange = (
  range: DateRange,
  // 弱警告已修改 修改人 Miya 修改日期：2025/7/22
  format = "YYYY-MM-DD"
) => {
  if (!range || !range[0] || !range[1]) {
    return "";
  }

  const [start, end] = range;
  if (start.isSame(end, 'day')) {
    return start.format(format);
  }

  return `${start.format(format)} ~ ${end.format(format)}`;
};

// 工具函数：将字符串转换为日期范围
export const parseDateRange = (
  dateString: string,
  // 弱警告已修改 修改人 Miya 修改日期：2025/7/22
  format = "YYYY-MM-DD"
) => {
  if (!dateString) {
    return null;
  }

  const parts = dateString.split(' ~ ');
  if (parts.length === 1) {
    // 单个日期
    const date = dayjs(parts[0], format);
    return date.isValid() ? [date, date] : null;
  }
  if (parts.length === 2) {
    // 日期范围
    const start = dayjs(parts[0], format);
    const end = dayjs(parts[1], format);
    return (start.isValid() && end.isValid()) ? [start, end] : null;
  }

  return null;
};

export default DateRangePicker;