/**
 * 序列数据管理Hook集合
 *
 * @fileoverview 提供基金序列数据和基金经理数据的查询功能，支持智能重试和错误处理
 * <AUTHOR>
 * @since 2025-07-22
 * @version 1.0.0
 *
 * 功能特性：
 * - 基金序列数据查询（代码、标签、收藏状态）
 * - 基金经理详细数据查询
 * - 智能重试策略（502错误不重试）
 * - React Query集成
 * - 缓存管理和状态跟踪
 * - 错误处理和状态管理
 */

import { useQuery } from "@tanstack/react-query";
import { fetchSequenceData, fetchFundManagerData } from "../lib/sequence_data";
import type { SequenceParams, SequenceResponse, FundManagerParams, FundManagerResponse } from "../lib/sequence_data";

/**
 * 序列数据查询Hook返回值接口
 */
export interface UseSequenceDataReturn {
	/** 序列数据响应 */
	data: SequenceResponse | undefined;
	/** 是否正在初次加载 */
	isLoading: boolean;
	/** 是否正在获取数据（包括后台刷新） */
	isFetching: boolean;
	/** 错误对象，无错误时为null */
	error: Error | null;
	/** 是否发生错误 */
	isError: boolean;
	/** 是否成功获取数据 */
	isSuccess: boolean;
	/** 手动重新获取数据的函数 */
	refetch: () => Promise<any>;
	/** 是否正在重新获取数据 */
	isRefetching: boolean;
}

/**
 * 基金经理数据查询Hook返回值接口
 */
export interface UseFundManagerDataReturn {
  /** 基金经理数据响应 */
  data: FundManagerResponse | undefined;
  /** 是否正在初次加载 */
  isLoading: boolean;
  /** 是否正在获取数据（包括后台刷新） */
  isFetching: boolean;
  /** 错误对象，无错误时为null */
  error: Error | null;
  /** 是否发生错误 */
  isError: boolean;
  /** 是否成功获取数据 */
  isSuccess: boolean;
  /** 手动重新获取数据的函数 */
  refetch: () => Promise<any>;
  /** 是否正在重新获取数据 */
  isRefetching: boolean;
}

/**
 * 查询投资人基金持股数据的Hook
 *
 * 获取基金序列数据，包含投资者代码、标签和收藏状态信息
 *
 * @param params - 查询参数对象
 * @param params.organizationId - 组织ID（必填）
 * @param options - React Query配置选项
 * @param options.enabled - 是否启用查询，默认为true
 * @param options.staleTime - 数据保持新鲜的时间，默认为0
 * @param options.gcTime - 垃圾回收时间，默认为10分钟
 * @returns 序列数据查询结果和相关状态
 *
 * 特殊处理：
 * - 对于502错误（股票代码不存在）不进行重试
 * - 其他错误最多重试2次
 *
 */
export function useSequenceData(
  params: SequenceParams,
  options: { enabled?: boolean; staleTime?: number; gcTime?: number } = {}
): UseSequenceDataReturn {
  const {
    enabled = true,
    staleTime = 0,
    gcTime = 10 * 60 * 1000,
  } = options;

  const query = useQuery<SequenceResponse, Error>({
    queryKey: ["sequenceData", params.organizationId],
    queryFn: () => fetchSequenceData(params),
    enabled: enabled && !!params.organizationId,
    refetchOnWindowFocus: false,
    staleTime,
    gcTime,
    retry: (failureCount, error) => {
      // 对于 502 错误（股票代码不存在），不进行重试
      if (error.message.includes('502')) {
        return false;
      }
      // 其他错误最多重试 2 次
      return failureCount < 2;
    },
  });

  return {
    data: query.data,
    isLoading: query.isLoading,
    isFetching: query.isFetching,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    refetch: query.refetch,
    isRefetching: query.isRefetching,
  };
}

/**
 * 查询基金经理数据的 hook
 * @param params 查询参数，参见 FundManagerParams
 * @param options react-query 配置项（可选）
 * @returns UseFundManagerDataReturn
 *
 * 修改时间: 2025-07-11
 * 修改人: Miya
 * 关联需求: 查询基金经理数据
 * 恢复方法: 删除本文件新增内容
 */
export function useFundManagerData(
  params: FundManagerParams,
  options: { enabled?: boolean; staleTime?: number; gcTime?: number; refreshKey?: number } = {}
): UseFundManagerDataReturn {
  const {
			enabled = true,
			staleTime = 0,
			gcTime = 10 * 60 * 1000,
			refreshKey,
		} = options;

  const query = useQuery<FundManagerResponse, Error>({
    queryKey: ["fundManagerData", params.investors.sort().join(','), refreshKey],
    queryFn: () => fetchFundManagerData(params),
    enabled: enabled && !!params.investors && params.investors.length > 0,
    refetchOnWindowFocus: false,
    staleTime,
    gcTime,
  });

  return {
    data: query.data,
    isLoading: query.isLoading,
    isFetching: query.isFetching,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    refetch: query.refetch,
    isRefetching: query.isRefetching,
  };
}
