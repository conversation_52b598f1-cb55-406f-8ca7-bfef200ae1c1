import * as XLSX from 'xlsx';
import mammoth from 'mammoth';

/**
 * 支持的文件类型
 */
export const SUPPORTED_FILE_TYPES = ['.doc', '.docx', '.xls', '.xlsx', '.txt'];

/**
 * 文件大小限制 (50MB)
 */
export const MAX_FILE_SIZE = 50 * 1024 * 1024;

/**
 * 文件解析结果接口
 */
export interface ParsedFileResult {
  fileName: string;
  fileType: string;
  parsedContent: any;
}

/**
 * 检查文件类型是否支持
 */
export function isSupportedFileType(fileName: string): boolean {
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  return SUPPORTED_FILE_TYPES.includes(extension);
}

/**
 * 获取文件扩展名
 */
export function getFileExtension(fileName: string): string {
  return fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
}

/**
 * TXT文件解析函数
 */
export function parseTxtFile(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (event) => {
      try {
        const content = event.target?.result as string;
        if (content) {
          resolve(content);
        } else {
          reject(new Error('文件内容为空'));
        }
      } catch (error) {
        reject(new Error(`TXT文件解析失败: ${error}`));
      }
    };

    reader.onerror = () => {
      reject(new Error('文件读取失败'));
    };

    // 使用UTF-8编码读取文本文件
    reader.readAsText(file, 'UTF-8');
  });
}

/**
 * Excel文件解析函数
 */
export function parseExcelFile(file: File): Promise<any> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (event) => {
      try {
        const data = event.target?.result;
        if (!data) {
          reject(new Error('文件内容为空'));
          return;
        }

        // 读取工作簿
        const workbook = XLSX.read(data, { type: 'array' });
        const result: any = {};

        // 解析所有工作表
        workbook.SheetNames.forEach(sheetName => {
          const worksheet = workbook.Sheets[sheetName];
          // 将工作表转换为JSON格式
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
          result[sheetName] = jsonData;
        });

        resolve({
          sheets: result,
          sheetNames: workbook.SheetNames,
          totalSheets: workbook.SheetNames.length
        });
      } catch (error) {
        reject(new Error(`Excel文件解析失败: ${error}`));
      }
    };

    reader.onerror = () => {
      reject(new Error('文件读取失败'));
    };

    // 读取为ArrayBuffer
    reader.readAsArrayBuffer(file);
  });
}

/**
 * Word文档解析函数
 */
export function parseWordFile(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = async (event) => {
      try {
        const arrayBuffer = event.target?.result as ArrayBuffer;
        if (!arrayBuffer) {
          reject(new Error('文件内容为空'));
          return;
        }

        // 使用mammoth解析Word文档
        const result = await mammoth.extractRawText({ arrayBuffer });

        if (result.value) {
          resolve(result.value);
        } else {
          reject(new Error('无法提取文档内容'));
        }
      } catch (error) {
        reject(new Error(`Word文档解析失败: ${error}`));
      }
    };

    reader.onerror = () => {
      reject(new Error('文件读取失败'));
    };

    // 读取为ArrayBuffer
    reader.readAsArrayBuffer(file);
  });
}

/**
 * 统一的文件解析函数
 */
export async function parseFile(file: File): Promise<any> {
  const extension = getFileExtension(file.name);

  switch (extension) {
    case '.txt':
      return await parseTxtFile(file);
    case '.xls':
    case '.xlsx':
      return await parseExcelFile(file);
    case '.doc':
    case '.docx':
      return await parseWordFile(file);
    default:
      throw new Error(`不支持的文件格式: ${extension}`);
  }
}

/**
 * 批量解析文件
 */
export async function parseFiles(
  files: File[],
  onProgress?: (fileName: string, index: number, total: number) => void
): Promise<ParsedFileResult[]> {
  const results: ParsedFileResult[] = [];
  
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    const extension = getFileExtension(file.name);
    
    // 只解析支持的文件格式
    if (SUPPORTED_FILE_TYPES.includes(extension)) {
      onProgress?.(file.name, i + 1, files.length);
      
      try {
        const parsedContent = await parseFile(file);
        results.push({
          fileName: file.name,
          fileType: extension,
          parsedContent: parsedContent,
        });
      } catch (error) {
        throw new Error(`文件 ${file.name} 解析失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }
  }
  
  return results;
}

/**
 * 验证文件
 */
export function validateFile(file: File): { valid: boolean; error?: string } {
  if (!isSupportedFileType(file.name)) {
    return {
      valid: false,
      error: `${file.name}: 不支持的文件格式`
    };
  }
  
  if (file.size > MAX_FILE_SIZE) {
    return {
      valid: false,
      error: `${file.name}: 文件大小超过50MB限制`
    };
  }
  
  return { valid: true };
}

/**
 * 批量验证文件
 */
export function validateFiles(files: File[]): { validFiles: File[]; errors: string[] } {
  const validFiles: File[] = [];
  const errors: string[] = [];
  
  files.forEach(file => {
    const validation = validateFile(file);
    if (validation.valid) {
      validFiles.push(file);
    } else if (validation.error) {
      errors.push(validation.error);
    }
  });
  
  return { validFiles, errors };
}
