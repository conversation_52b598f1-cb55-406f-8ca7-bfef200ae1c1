# Database
DATABASE_URL=""
# ... if you use Supabase
DIRECT_URL=""

# Site url
NEXT_PUBLIC_SITE_URL=""

# Authentication
BETTER_AUTH_SECRET="A_RANDOM_SECRET_STRING"
# ... for Github
GITHUB_CLIENT_ID="YOUR_GITHUB_CLIENT_ID"
GITHUB_CLIENT_SECRET="YOUR_GITHUB_CLIENT_SECRET"
# ... for Google
GOOGLE_CLIENT_ID="YOUR_GOOGLE_CLIENT_ID"
GOOGLE_CLIENT_SECRET="YOUR_GOOGLE_CLIENT_SECRET"

# Mails
# ... with nodemailer
# MAIL_HOST="gz-smtp.qcloudmail.com"
# MAIL_PORT="465"
# MAIL_USER="<EMAIL>"
# MAIL_PASS="StarLink2025"
# ... with Plunk
PLUNK_API_KEY=""
# ... with Resend
RESEND_API_KEY=""
# ... with Postmark
POSTMARK_SERVER_TOKEN=""
# ... with Mailgun
MAILGUN_API_KEY=""
MAILGUN_DOMAIN=""

# Payments
# ... with Lemonsqueezy
LEMONSQUEEZY_API_KEY=""
LEMONSQUEEZY_WEBHOOK_SECRET=""
LEMONSQUEEZY_STORE_ID=""
# ... with Stripe
STRIPE_SECRET_KEY=""
STRIPE_WEBHOOK_SECRET=""
# ... with Chargebee
CHARGEBEE_SITE=""
CHARGEBEE_API_KEY=""
# ... with Creem
CREEM_API_KEY=""
CREEM_WEBHOOK_SECRET=""
# ... with Polar
POLAR_ACCESS_TOKEN=""
POLAR_WEBHOOK_SECRET=""

# Product price ids
NEXT_PUBLIC_PRICE_ID_PRO_MONTHLY="asdf"
NEXT_PUBLIC_PRICE_ID_PRO_YEARLY="asdf"
NEXT_PUBLIC_PRICE_ID_LIFETIME="asdf"

# Analytics
# ... for Pirsch
NEXT_PUBLIC_PIRSCH_CODE=""
# ... for Plausible
NEXT_PUBLIC_PLAUSIBLE_URL=""
# ... for Mixpanel
NEXT_PUBLIC_MIXPANEL_TOKEN=""
# ... for Google Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=""

# Storage
S3_ACCESS_KEY_ID=""
S3_SECRET_ACCESS_KEY=""
S3_ENDPOINT=""
S3_REGION=""
NEXT_PUBLIC_AVATARS_BUCKET_NAME="avatars"

# AI
# ... with OpenAI
OPENAI_API_KEY=""

#Tencent Meeting
# 腾讯会议配置相关参数
#appid,SDK id, secret id, secret key通过在腾讯会议后台“企业管理”，“企业自建应用”创建应用后获取具体值
TENCENT_APP_ID=""
TENCENT_SDK_ID=""""
TENCENT_SECRET_ID=""
TENCENT_SECRET_KEY=""
# operator id 为腾讯会议管理员账号的userid，用于高级权限 API 调用，如成员管理和获取会议信息
TENCENT_OPERATOR_ID=""


# 股东名册API加密 (Shareholder Registry API Encryption)
NEXT_PUBLIC_SHAREHOLDER_API_KEY="YOUR_PUBLIC_API_KEY_HERE"
NEXT_PUBLIC_SHAREHOLDER_API_SECRET="YOUR_SERVER_SIDE_SECRET_HERE"
NEXT_PUBLIC_SHAREHOLDER_API_IV="YOUR_PUBLIC_INITIALIZATION_VECTOR_HERE"

# 股东名册批量处理设置 (Shareholder Registry Batch Processing)
# 批量创建股东记录的批次大小，默认200
SHAREHOLDER_BATCH_SIZE="200"
# 前端分批上传数据的批次大小，应与后端保持一致
NEXT_PUBLIC_SHAREHOLDER_BATCH_SIZE="200"
# 批量更新股东记录的并发限制，默认20
SHAREHOLDER_CONCURRENCY_LIMIT="20"
# 事务超时时间（毫秒），默认30000
SHAREHOLDER_TRANSACTION_TIMEOUT="30000"

# n8n代理服务配置 (n8n Proxy Service Configuration)
# 主要的n8n服务器URL，包含webhook路径和版本
N8N_BASE_URL="http://192.168.138.244:6789/webhook/v1"
# 可选的多版本n8n服务器配置
N8N_BASE_URL1="http://192.168.138.244:6789/webhook-test/v1"
# 生产环境示例配置

# 用于配置请求超时时间，单位毫秒，默认120秒
N8N_MEETING_TIMEOUT = "120000"

