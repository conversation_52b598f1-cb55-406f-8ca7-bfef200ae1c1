/**
 * 联系人数据管理工具库
 *
 * @fileoverview 提供联系人数据的CRUD操作函数，包括查询、创建、更新和删除功能
 * <AUTHOR>
 * @since 2025-07-22
 * @version 1.0.0
 *
 * 功能特性：
 * - 联系人列表查询（支持搜索和分页）
 * - 联系人创建、更新、删除
 * - 数据加密传输
 * - 错误处理和验证
 * - 类型安全的接口定义
 */

import { encryptRequestData, generateSign, decryptData } from "@repo/utils/lib/crypto";

/**
 * 联系人列表查询请求参数接口
 */
export interface ContactsListParams {
  /** 组织ID，必填 */
  organizationId: string;
  /** 搜索关键词（姓名、手机号、邮箱），支持模糊搜索 */
  keyword?: string;
  /** 页码，正整数，默认1 */
  page?: number;
  /** 每页数量，正整数，最大100，默认10 */
  limit?: number;
}

/**
 * 联系人信息数据接口
 */
export interface Contact {
  /** 联系人ID */
  contactId: string;
  /** 组织ID */
  organizationId: string;
  /** 姓名 */
  name: string;
  /** 手机号码 */
  phoneNumber: string;
  /** 邮箱地址 */
  email: string;
  /** 地址 */
  address: string;
  /** 备注信息 */
  remarks: string;
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
  /** 创建者 */
  createdBy: string;
  /** 更新者 */
  updatedBy: string;
}

/**
 * 联系人列表响应数据类型
 */
export interface ContactsListResponse {
  total: number;
  page: number;
  limit: number;
  contacts: Contact[];
}

/**
 * 获取联系人列表的异步函数
 * @param {ContactsListParams} params - 查询参数
 * @returns {Promise<ContactsListResponse>} 联系人列表数据
 * @throws {Error} 请求或解密失败时抛出
 * @description
 * - 参数会被加密并签名
 * - 响应数据会被解密
 * - 仅处理200响应，其他状态抛出异常
 * - 错误码需由调用方处理
 *
 * 修改时间: 2025-07-10
 * 修改人: Miya
 * 关联需求: 获取联系人列表
 * 恢复方法: 删除本文件新增内容
 */
export async function fetchContactsList(params: ContactsListParams): Promise<ContactsListResponse> {
  // 1. 参数加密
  const encrypted = await encryptRequestData(params);

  // 2. 生成签名（使用加密后的内容）
  const sign = generateSign(encrypted);

  // 3. 发送POST请求
  const requestBody = { content: encrypted, sign };

  const res = await fetch("/api/investor-management/contacts/list", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-Sign": sign,
    },
    /* 修改块开始: 请求体字段名与后端统一
     * 修改范围: body由{ data: encrypted }改为{ content: encrypted, sign }
     * 修改时间: 2025-07-10
     * 对应计划步骤: 4
     * 修改人: Miya
     */
    body: JSON.stringify(requestBody),
    /* 修改块结束: 请求体字段名与后端统一
     * 修改时间: 2025-07-10
     */
  });

  if (!res.ok) {
    const errorText = await res.text();
    throw new Error(`请求失败: ${res.status} - ${errorText}`);
  }

  // 4. 解密响应（需先 parse 再断言类型）
  const result = await res.json();

  if (result.code !== 200) {
    throw new Error(result.message || "接口返回错误");
  }
  // decryptData 返回 string，需 JSON.parse
  const data = JSON.parse(decryptData(result.data)) as ContactsListResponse;
  // console.log("fetchContactsList data", data);
  return data;
}

/**
 * 创建联系人请求参数
 */
export interface CreateContactParams {
  organizationId: string; //必填
  name: string; //必填
  phoneNumber?: string; //可选
  email?: string; //可选
  address?: string; //可选
  remarks?: string; //可选
}

/**
 * 创建联系人响应数据类型
 */
export interface CreateContactResponse {
  contactId: string;
}

/**
 * 创建联系人异步函数
 * @param {CreateContactParams} params - 创建参数
 * @returns {Promise<CreateContactResponse>} 创建结果，含contactId
 * @throws {Error} 请求或解密失败时抛出
 * @description
 * - 参数会被加密并签名
 * - 响应数据会被解密
 * - 仅处理200响应，其他状态抛出异常
 * - 错误码需由调用方处理
 *
 * 修改时间: 2025-07-10
 * 修改人: Miya
 * 关联需求: 创建联系人
 * 恢复方法: 删除本文件新增内容
 */
export async function createContact(params: CreateContactParams): Promise<CreateContactResponse> {
  // 1. 参数加密（需要 await）
  const encrypted = await encryptRequestData(params);

  // 2. 生成签名（使用加密后的内容）
  const sign = generateSign(encrypted);

  // 3. 发送POST请求
  const requestBody = { content: encrypted, sign };

  const res = await fetch("/api/investor-management/contacts/create", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-Sign": sign,
    },
    body: JSON.stringify(requestBody),
  });
  if (!res.ok) {
    const errorText = await res.text();
    throw new Error(`请求失败: ${res.status} - ${errorText}`);
  }

  // 4. 解密响应（需先 parse 再断言类型）
  const result = await res.json();

  if (result.code !== 200) {
    throw new Error(result.message || "接口返回错误");
  }
  // decryptData 返回 string，需 JSON.parse
  const data = JSON.parse(decryptData(result.data)) as CreateContactResponse;
  return data;
}

/**
 * 更新联系人请求参数
 */
export interface UpdateContactParams {
  contactId: string;
  organizationId: string;
  name: string; // name是必填的
  phoneNumber?: string;
  email?: string;
  address?: string;
  remarks?: string;
}

/**
 * 更新联系人响应数据类型
 */
export interface UpdateContactResponse {
  contactId: string;
}

/**
 * 更新联系人异步函数
 * @param {UpdateContactParams} params - 更新参数
 * @returns {Promise<UpdateContactResponse>} 更新结果，含contactId
 * @throws {Error} 请求或解密失败时抛出
 * @description
 * - 参数会被加密并签名
 * - 响应数据会被解密
 * - 仅处理200响应，其他状态抛出异常
 * - 错误码需由调用方处理
 *
 * 修改时间: 2025-07-10
 * 修改人: Miya
 * 关联需求: 更新联系人
 * 恢复方法: 删除本文件新增内容
 */
export async function updateContact(params: UpdateContactParams): Promise<UpdateContactResponse> {
  // console.log("updateContact params", params);
  // 1. 参数加密（需要 await）
  const encrypted = await encryptRequestData(params);

  // 2. 生成签名（使用加密后的内容）
  const sign = generateSign(encrypted);

  // 3. 发送POST请求
  const requestBody = { content: encrypted, sign };

  const res = await fetch("/api/investor-management/contacts/update", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-Sign": sign,
    },
    body: JSON.stringify(requestBody),
  });
  if (!res.ok) {
    const errorText = await res.text();
    throw new Error(`请求失败: ${res.status} - ${errorText}`);
  }

  // 4. 解密响应（需先 parse 再断言类型）
  const result = await res.json();

  if (result.code !== 200) {
    throw new Error(result.message || "接口返回错误");
  }
  // decryptData 返回 string，需 JSON.parse
  const data = JSON.parse(decryptData(result.data)) as UpdateContactResponse;
  return data;
}

/**
 * 删除联系人请求参数
 */
export interface DeleteContactParams {
  contactId: string;
  organizationId: string;
}

/**
 * 删除联系人响应数据类型
 */
export interface DeleteContactResponse {
  contactId: string;
}

/**
 * 删除联系人异步函数
 * @param {DeleteContactParams} params - 删除参数
 * @returns {Promise<DeleteContactResponse>} 删除结果，含contactId
 * @throws {Error} 请求或解密失败时抛出
 * @description
 * - 参数会被加密并签名
 * - 响应数据会被解密
 * - 仅处理200响应，其他状态抛出异常
 * - 错误码需由调用方处理
 *
 * 修改时间: 2025-07-10
 * 修改人: Miya
 * 关联需求: 删除联系人
 * 恢复方法: 删除本文件新增内容
 */
export async function deleteContact(params: DeleteContactParams): Promise<DeleteContactResponse> {

  // 1. 参数加密（需要 await）
  const encrypted = await encryptRequestData(params);

  // 2. 生成签名（使用加密后的内容）
  const sign = generateSign(encrypted);

  // 3. 发送POST请求
  const requestBody = { content: encrypted, sign };

  const res = await fetch("/api/investor-management/contacts/delete", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-Sign": sign,
    },
    body: JSON.stringify(requestBody),
  });
  if (!res.ok) {
    //bug:错误扑捉
				const errorText = await res.text();
    throw new Error(`请求失败: ${res.status} - ${errorText}`);
  }

  // 4. 解密响应（需先 parse 再断言类型）
  const result = await res.json();
  //bug:错误扑捉
  if (result.code !== 200) {
    throw new Error(result.message || "接口返回错误");
  }
  // decryptData 返回 string，需 JSON.parse
  const data = JSON.parse(decryptData(result.data)) as DeleteContactResponse;
  return data;
}
