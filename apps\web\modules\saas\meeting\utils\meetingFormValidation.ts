import { z } from "zod";
import { validateContactFormat } from "./guestValidation";

/**
 * 验证开始时间是否不早于当前时间
 */
export function validateStartTime(date: string, startTime: string): boolean {
  const now = new Date();
  const startDateTime = new Date(`${date}T${startTime}:00`);
  return startDateTime > now;
}

/**
 * 验证结束时间是否晚于开始时间至少15分钟
 */
export function validateTimeRange(date: string, startTime: string, endTime: string): boolean {
  const startDateTime = new Date(`${date}T${startTime}:00`);
  const endDateTime = new Date(`${date}T${endTime}:00`);
  const diffInMinutes = (endDateTime.getTime() - startDateTime.getTime()) / (1000 * 60);
  return diffInMinutes >= 15;
}

/**
 * 会议表单验证 Schema
 */
export const meetingFormSchema = z.object({
  title: z.string().min(2, "会议标题至少需要2个字符").max(100, "会议标题不能超过100个字符"),
  date: z.string().min(1, "请选择会议日期"),
  startTime: z.string().min(1, "请选择开始时间"),
  endTime: z.string().min(1, "请选择结束时间"),
  location: z.string().optional(),
  isRecurring: z.boolean().default(false),
  timezone: z.string().min(1, "请选择时区"),
  password: z.string().optional(),
  requirePassword: z.boolean().default(false),
  muteParticipantsOnEntry: z.enum(["muteOn", "muteOff", "muteAuto"]).default("muteOn"),
  waitingRoom: z.boolean().default(false),
  recordMeeting: z.boolean().default(false),
  allowChat: z.boolean().default(true),
  enterInAdvance: z.boolean().default(true),
  multiPlatform: z.boolean().default(true),
  allowScreenShare: z.boolean().default(true),
  allowParticipantsToRename: z.boolean().default(true),
  enableHostKey: z.boolean().default(false),
  hostKey: z.string().optional(),
  enableDocUpload: z.boolean().default(true),
  screenWatermark: z.boolean().default(false),
  disableScreenshot: z.boolean().default(false),
  autoTranscribe: z.boolean().default(false),
  playIvrOnJoin: z.boolean().default(false),
  playIvrOnLeave: z.boolean().default(false),
  onlyEnterpriseUserAllowed: z.boolean().default(false),
  recurringType: z.number().default(0),
  untilType: z.number().default(0),
  untilDate: z.string().optional(),
  untilCount: z.number().min(1).max(200).optional(),
  guests: z.array(z.object({
    area: z.string().min(1, "请选择国家/地区代码"),
    phone_number: z.string().min(1, "请输入联系方式"),
    guest_name: z.string().optional(),
  }).refine((guest) => {
    // 使用新的联系方式验证函数
    if (!guest.phone_number.trim()) return true; // 空值由 min(1) 处理
    const validation = validateContactFormat(guest.area, guest.phone_number);
    return validation.valid;
  }, {
    message: "联系方式格式不正确",
    path: ["phone_number"],
  })).max(2000, "嘉宾数量不能超过2000人").default([]),
  enable_enroll: z.boolean().default(false),
  approval_mode: z.enum(["manual", "auto"]).default("manual"),
}).refine((data) => {
  // 验证开始时间不能早于当前时间
  return validateStartTime(data.date, data.startTime);
}, {
  message: "开始时间不能早于当前时间",
  path: ["startTime"],
}).refine((data) => {
  // 验证结束时间是否晚于开始时间至少15分钟
  return validateTimeRange(data.date, data.startTime, data.endTime);
}, {
  message: "结束时间必须比开始时间晚至少15分钟",
  path: ["endTime"],
});

/**
 * 表单数据类型
 */
export type MeetingFormValues = z.infer<typeof meetingFormSchema>;
