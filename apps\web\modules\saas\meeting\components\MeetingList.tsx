"use client";

import { useState, useEffect, useRef } from "react";
// import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@ui/components/select";
//修改人：Miya 修改日期：2025/7/25
//修改说明：删去未引用到的图标
import {  X as XIcon, RefreshCcw as RefreshCcwIcon,Calendar as CalendarIcon } from "lucide-react";
import Link from "next/link";
import type{ Meeting } from "./types";
import { MeetingTable } from "./MeetingTable";
import { formatMeetingData, dateStringToTimestamp, getDefaultDateRange } from "./meeting-utils";
import { ActiveOrganizationContext } from "../../organizations/lib/active-organization-context";
import { useSession } from "../../auth/hooks/use-session";
import React from "react";
import { toast } from "sonner";
import type { CreateUserParams } from "@repo/api/src/routes/meeting/lib/account";
import { cn } from "@ui/lib";
import { DateFilter } from "./meeting-utils";


/**
 * 会议列表组件
 * 
 * @param organizationSlug - 组织的 slug
 * @returns 渲染的列表组件
 */
export function MeetingList({ organizationSlug }: { organizationSlug: string }) {
  const activeOrganizationContext = React.useContext(ActiveOrganizationContext);
  const { user } = useSession();
  const org_slug = activeOrganizationContext?.activeOrganization?.slug;
  const [activeTab, setActiveTab] = useState<
  "upcoming" | "ongoing" | "completed"
  >("upcoming");
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState("会议号");
  const [filteredMeetings, setFilteredMeetings] = useState<Meeting[]>([]);
  const [upcomingMeetings, setUpcomingMeetings] = useState<Meeting[]>([]);
  const [ongoingMeetings, setOngoingMeetings] = useState<Meeting[]>([]);
  const [completedMeetings, setCompletedMeetings] = useState<Meeting[]>([],);
  const [isLoadingUpcoming, setIsLoadingUpcoming] = useState(true);
  const [isLoadingOngoing, setIsLoadingOngoing] = useState(true);
  const [isLoadingCompleted, setIsLoadingCompleted] = useState(true);
  const [refreshTrigger, setRefreshTrigger] = useState(0); // 触发刷新的状态
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">(
  "desc",
  ); // 排序方向, 默认降序(最新的在前)
  const [startDate, setStartDate] = useState<string>("");
  const [endDate, setEndDate] = useState<string>("");


  // 添加部门相关状态
  const [departments, setDepartments] = useState<any[]>([]);
  const [isLoadingDepartments, setIsLoadingDepartments] = useState(false);
  const [departmentError, setDepartmentError] = useState<string | null>(null);

  // 添加用户相关状态
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const [userError, setUserError] = useState<string | null>(null);

  // 添加激活状态相关状态
  const [isActivating, setIsActivating] = useState(false);
  const [showActivationDialog, setShowActivationDialog] = useState(false);
  const [activationWindow, setActivationWindow] = useState<Window | null>(
  null,
  );
  const [activationCheckInterval, setActivationCheckInterval] =useState<NodeJS.Timeout | null>(null);

  // 添加用户状态监控
  const [isUserActive, setIsUserActive] = useState(false);
  const [isCheckingUserStatus, setIsCheckingUserStatus] = useState(false); // 是否正在检查用户状态
  const [hasCheckedUserStatus, setHasCheckedUserStatus] = useState(false); // 是否已完成用户状态检查

  // 添加参会者搜索相关状态
  const [isSearchingParticipant, setIsSearchingParticipant] =
  useState(false);
  
  // 获取部门列表
  // const fetchDepartments = async () => {
  //   setIsLoadingDepartments(true);
  //   setDepartmentError(null);
  //   try {
  //     const response = await fetch('/api/meetings/departments');
  //     const data = await response.json();
      
  //     if (!data.success) {
  //       throw new Error(data.error || '获取部门列表失败');
  //     }
      
  //     setDepartments(data.data.department_list || []);
  //     return data.data.department_list || [];
  //   } catch (error: any) {
  //     toast.error('获取部门列表错误:', error);
  //     setDepartmentError(error.message);
  //     return [];
  //   } finally {
  //     setIsLoadingDepartments(false);
  //   }
  // };

  // // 创建部门
  // const handleCreateDepartment = async (departmentName: string) => {
  //   try {
  //     // 先检查部门是否已存在
  //     // const departmentExists = departments.some(
  //     //   (dept: Department) => dept.department_name === departmentName
  //     // );

  //     // if (departmentExists) {
  //     //   console.log('部门已存在，跳过创建:', departmentName);
  //     //   return;
  //     // }

  //     // 创建新部门
  //     const response = await fetch('/api/meetings/departments', {
  //       method: 'POST',
  //       headers: {
  //         'Content-Type': 'application/json',
  //       },
  //       body: JSON.stringify({ departmentName }),
  //     });

  //     const data = await response.json();
      
  //     if (!data.success) {
  //       throw new Error(data.error || '创建部门失败');
  //     }

  //     // 刷新部门列表
  //     await fetchDepartments();
  //   } catch (error: any) {
  //     toast.error('创建部门错误:', error.message);
  //     setDepartmentError(error.message);
  //   }
  // };

  // 获取用户列表
  const fetchUsers = async () => {
    setIsLoadingUsers(true);
    setUserError(null);
    try {
      const response = await fetch(`/api/meetings/users?userId=${user?.id}`);
      const res = await response.json();
      if (!res.success) {
        // 抛出包含完整错误信息的错误对象
        const error = new Error('获取用户列表失败');
        (error as any).errorData = res.error;
        throw error;
      }
      return res.exists;
      // const userList = res.data.data.users;
      // setUsers(userList);
      // return userList;
    } catch (error: any) {
      /* 修改于2025-06-12: 正确解析错误响应并判断特定错误码500125 */
      /* 原问题: 需要正确提取错误响应中的错误码和消息信息 */
      /* 修改范围: 重新设计错误处理逻辑，从error.errorData中提取错误信息 */
      /* 恢复方法: 删除新的错误处理逻辑，恢复原有的简单错误处理 */
      
      // 检查错误对象是否包含API返回的错误数据
      const errorData = error.errorData;
      const errorCode = errorData?.error_info?.error_code;
      const errorMessage = errorData?.error_info?.message;
      
      console.log('获取用户列表错误', errorData || error);
      
      // 特别处理错误码500125（IP白名单限制）
      if (errorCode === 500125) {
        toast.error(`访问受限：${errorMessage || 'IP地址不在白名单中'}。请联系管理员寻求帮助`);
        setUserError(errorMessage || 'IP地址不在白名单中');
      } else if (errorMessage) {
        // 有具体错误消息的情况
        toast.error(`获取用户列表失败：${errorMessage}`);
        setUserError(errorMessage);
      } else {
        // 处理其他类型的错误
        toast.error(`获取用户列表错误：${error.message || '网络请求失败'}`);
        setUserError(error.message || '获取用户列表失败');
      }
      
      return [];
    } finally {
      setIsLoadingUsers(false);
    }
  };

  // 添加获取激活链接的函数
  const getActivationLink = async (userId: string) => {
    try {
      const response = await fetch('/api/meetings/invite-activate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          useridList: [userId]
        }),
      });

      const data = await response.json();
      // console.log('获取激活链接响应:', data);
      
      if (!data.success) {
        throw new Error(data.error || '获取激活链接失败');
      }

      return data.data;
    } catch (error: any) {
      toast.error('获取激活链接错误:', error);
      throw error;
    }
  };

  

  // 清理激活检查定时器
  const clearActivationCheck = () => {
    if (activationCheckInterval) {
      clearInterval(activationCheckInterval);
      setActivationCheckInterval(null);
    }
  };

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      clearActivationCheck();
    };
  }, []);

  // 处理激活链接
  const handleActivation = async () => {
    if (!user?.id || !org_slug) {
      toast.error('用户信息或组织信息不存在');
      return;
    }
    
    setIsActivating(true);
    try {
      // 检查用户是否已存在且只是未激活状态
      const existingUser = await fetchUsers();
      if (!existingUser.exists) {
        //省略部门相关操作
        // const existingDepartment = departments.find(
        //   (dept: Department) => dept.department_name === org_slug
        // );
        // if (!existingDepartment){
        //   // 1. 创建部门
        //   await handleCreateDepartment(org_slug);
        // }
        
        
        // // 2. 获取最新部门列表以获取部门ID
        // const latestDepartments = await fetchDepartments();
        // const targetDepartment = latestDepartments.find(
        //   (dept: Department) => dept.department_name === org_slug
        // );
        
        // if (!targetDepartment) {
        //   throw new Error('创建部门失败');
        // }
        
        // 3. 创建用户
        await handleCreateUser({
          username: user.name || user.email?.split("@")[0] || "User",
          userid: user.id,
          email: user.email,
          // department_list: [targetDepartment.department_id],
          auto_invite: true,
          area: "86",
        });
        
      }
      
      // 5. 获取激活链接
      const activationData = await getActivationLink(user.id);
      
      if (activationData?.data.inactivate_user_list?.[0]?.activate_url) {
        // 显示激活对话框
        setShowActivationDialog(true);
        
        // 打开激活链接
        const newWindow = window.open(activationData.data.inactivate_user_list[0].activate_url, '_blank');
        setActivationWindow(newWindow);
        
        // 设置定时器，每5秒检查一次状态
        const interval = setInterval(async () => {
          try {
            await checkUserStatus();
            // 如果用户已激活，清除定时器并关闭对话框
            if (isUserActive) {
              clearActivationCheck();
              setShowActivationDialog(false);
              setActivationWindow(null);
              // 刷新页面
              window.location.reload();
            }
          } catch (error:any) {
            console.error("检查用户状态失败:", error);
          }
        }, 5000);

        setActivationCheckInterval(interval);
      }
    } catch (error:any) {
      toast.error('激活过程失败:', error);
    } finally {
      setIsActivating(false);
    }
  };

  // 创建用户
  const handleCreateUser = async (params: CreateUserParams) => {
    try {
      // 先检查用户是否已存在
      // const userExists = users.some((u: User) => 
      //   u.userid === params.userid || u.email === params.email
      // );

      // if (userExists) {
      //   console.log('用户已存在，跳过创建:', params.userid);
      //   return;
      // }

      const response = await fetch('/api/meetings/create/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: params.username,
          userid: params.userid,
          email: params.email,
          department_list: params.department_list,
          auto_invite: params.auto_invite,
          area: params.area
        }),
      });

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error?.message || data.error || '创建用户失败');
      }

      // 刷新用户列表
      await fetchUsers();

      // 获取激活链接
      // try {
      //   const activationData = await getActivationLink(params.userid);
      //   console.log('用户激活链接:', activationData);
      // } catch (error) {
      //   toast.error('获取激活链接失败:', error);
      // }
    } catch (error: any) {
      toast.error('创建用户错误:', error.meesage);
      setUserError(error.message);
    }
  };

  // 添加一个ref来跟踪是否已经初始化过
  const initializationRef = useRef(false);
  const initializationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 初始化部门和用户
  useEffect(() => {
    const initializeDepartmentAndUser = async () => {
      // 如果已经初始化过，直接返回
      if (initializationRef.current) {
        return;
      }

      if (
        !user ||
        !org_slug ||
        // isLoadingDepartments ||
        isLoadingUsers
      ) {
        return;
      }

      // 标记为正在初始化
      initializationRef.current = true;
      console.log("开始初始化部门和用户");

      try {
        // 获取部门列表
        // const latestDepartments = await fetchDepartments();

        // 检查用户是否存在
        const userExists = await fetchUsers();
        setRefreshTrigger((prev) => prev + 1);

        if (!userExists.exists) {
          console.log("用户不存在，等待创建:", user.email);
          setIsUserActive(false);
          return;
        }


        setIsUserActive(userExists.activated);



        // // 检查部门是否存在
        // const targetDepartment = latestDepartments.find(
        // 	(dept: Department) => dept.department_name === org_slug,
        // );

        // // 部门不存在，创建部门，但不阻塞用户查看会议列表
        // if (!targetDepartment) {
        // 	await handleCreateDepartment(org_slug);
        // }

        // // 部门已存在 查看用户是否有所属部门

        // const targetUser = latestUsers.find((u: User) => u.userid === user.id);

        // // 用户没有所属部门，则将用户添加到部门中
        // if (targetUser?.department_list.length === 0) {
        //   const allDepartments = await fetchDepartments();
        //   const targetDepartment = allDepartments.find(
        // 								(dept: Department) =>
        // 									dept.department_name ===
        // 									org_slug,
        // 							);
        //   await UpdateDepartment(targetDepartment?.department_id || "测试部门id");
        // }

        // await fetchUsers();

      } catch (error:any) {
        toast.error("初始化检查失败:", error);
        setIsUserActive(false);
        // 如果出错，重置初始化标记，允许重试
        initializationRef.current = false;
      } finally {
        setHasCheckedUserStatus(true);
      }
    };

    // 清除之前的定时器
    if (initializationTimeoutRef.current) {
      clearTimeout(initializationTimeoutRef.current);
    }

    // 使用防抖机制，延迟100ms执行，避免快速连续调用
    initializationTimeoutRef.current = setTimeout(() => {
      initializeDepartmentAndUser();
    }, 100);

    // 清理函数
    return () => {
      if (initializationTimeoutRef.current) {
        clearTimeout(initializationTimeoutRef.current);
      }
    };
  }, [user?.id, org_slug]); // 使用更具体的依赖项，避免user对象引用变化导致的重复执行



  // 设置默认日期，防止水合问题
  useEffect(() => {
			// const today = new Date();
			// const oneMonthAgo = new Date(today);
			// oneMonthAgo.setMonth(today.getMonth() - 1);

			// const startDateStr = oneMonthAgo.toISOString().split("T")[0];
			// const endDateStr = today.toISOString().split("T")[0];

			// setStartDate(startDateStr);
			// setEndDate(endDateStr);
      const defaultRange = getDefaultDateRange(activeTab);
      setStartDate(defaultRange.startDate);
      setEndDate(defaultRange.endDate);
		}, [activeTab]);
  
  
  
  

  /* 修改块开始: 会议列表API调用优化
   * 修改范围: 传入真实用户ID而非使用固定operatorId
   * 对应需求: 使用user.id获取用户专属的会议列表
   * 恢复方法: 删除userId参数，恢复原有的固定API调用
   */
  // 获取即将召开的会议
  useEffect(() => {
    // 如果用户信息不存在，不进行API调用
    if (!user?.id) {
      setIsLoadingUpcoming(false);
      setIsLoadingOngoing(false);
      return;
    }

    // 检查用户是否已在系统中创建
    const checkUserAndFetchMeetings = async () => {
      try {
        // 使用已获取的用户列表检查用户是否存在
          // const userExists = users.some(
          //   (u: User) => u.userid === user.id,
          // );

          
          if (!isUserActive) {
            // console.log('用户尚未在系统中创建，等待用户创建完成');
            setIsLoadingUpcoming(false);
            setIsLoadingOngoing(false);
            return;
          }

          // 用户存在，继续获取会议列表
          setIsLoadingUpcoming(true);
          setIsLoadingOngoing(true);

          const meetingsResponse = await fetch(
            `/api/meetings/upcoming?userId=${user.id}`,
          );
          if (!meetingsResponse.ok) {
            const errorText =
              await meetingsResponse.text();
            throw new Error(
              `状态 ${meetingsResponse.status}: ${errorText}`,
            );
          }

          const payload = await meetingsResponse.json();
          if (!payload.success) {
            throw new Error(
              JSON.stringify(payload.error),
            );
          }

          // 将API返回的meeting_info_list转换为Meeting类型
          if (payload.data?.meeting_info_list) {
            const convertedMeetings = payload.data.meeting_info_list.map((item: any) => formatMeetingData(item));

            // 区分进行中和即将召开的会议
            const upcoming = convertedMeetings.filter((meeting: Meeting) =>
              meeting.status === "MEETING_STATE_INIT");

            const ongoing = convertedMeetings.filter((meeting: Meeting) =>
              meeting.status === "MEETING_STATE_STARTED");

            // console.log(`过滤出即将召开的会议: ${upcoming.length}个, 进行中的会议: ${ongoing.length}个`);

            setUpcomingMeetings(upcoming);
            setOngoingMeetings(ongoing);
          } else {
            // 如果没有会议数据，设置为空数组
            setUpcomingMeetings([]);
            setOngoingMeetings([]);
          }
								
      } catch (err: any) {
        toast.error("获取会议列表错误:", err);
        // 获取失败时使用空数组
        setUpcomingMeetings([]);
        setOngoingMeetings([]);
        
        // 检查是否是用户未创建的错误
        if (err.message?.includes("user not found") || err.message?.includes("用户不存在")) {
          console.log("用户尚未创建，等待用户创建完成");
          return;
        }
        
        // 其他错误处理
        if (err.message?.includes("network") || err.message?.includes("Network")) {
          toast.error("网络连接错误，请检查网络连接");
          return;
        }
        
        // 记录其他类型的错误
        toast.error("获取会议列表时发生未知错误:", err);
      } finally {
        setIsLoadingUpcoming(false);
        setIsLoadingOngoing(false);
      }
    };
    if(activeTab === "upcoming" || activeTab === "ongoing") {
      checkUserAndFetchMeetings();
    }
  }, [refreshTrigger, user?.id, isUserActive]); // 使用isUserActive而不是users数组，避免不必要的重复调用

  // 获取历史会议
  useEffect(() => {
    // 如果用户信息不存在，不进行API调用
    if (!user?.id) {
      setIsLoadingCompleted(false);
      return;
    }

    // 检查用户是否已在系统中创建
    const checkUserAndFetchHistory = async () => {
      try {
        // 使用已获取的用户列表检查用户是否存在
        // const userExists = users.some((u: User) => u.userid === user.id);
        
        if (!isUserActive) {
          // console.log('用户尚未在系统中创建，等待用户创建完成');
          setIsLoadingCompleted(false);
          return;
        }

        // 用户存在，继续获取历史会议
        setIsLoadingCompleted(true);

        // 构建API查询参数
        let apiUrl = `/api/meetings/history?pageSize=20&page=1&userId=${user.id}`;

        // 仅针对历史会议添加时间筛选参数
        if (startDate && endDate) {
          if (startDate > endDate) {
            toast.error("开始日期不能晚于结束日期");
            return;
          }
          const startDateObj = new Date(startDate);
          const endDateObj = new Date(endDate);
          const diffTime = Math.abs(endDateObj.getTime() - startDateObj.getTime());
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          if (diffDays > 90  && activeTab === "completed") {
            toast.error("间隔不能超过九十天");
            return;
          }
          const startTimestamp = dateStringToTimestamp(startDate, false);
          const endTimestamp = dateStringToTimestamp(endDate, true);
          console.log(startTimestamp, endTimestamp);
          apiUrl += `&startTime=${startTimestamp}&endTime=${endTimestamp}`;
        }

        const historyResponse = await fetch(apiUrl);
        if (!historyResponse.ok) {
          const errorText = await historyResponse.text();
          throw new Error(`状态 ${historyResponse.status}: ${errorText}`);
        }
        
        const payload = await historyResponse.json();
        if (!payload.success) {
          throw payload;
        }
        
        // console.log("历史会议API返回数据:", payload.data);
        
        // 将API返回的meeting_info_list转换为Meeting类型
        if (payload.data?.meeting_info_list?.length > 0) {
          const convertedMeetings = payload.data.meeting_info_list.map((item: any) => formatMeetingData(item));
          
        
        setCompletedMeetings(convertedMeetings);
        } else {
          // console.warn("历史会议数据中未找到有效的meeting_info_list字段或数据为空:", payload.data);
          setCompletedMeetings([]);
        }
        
      } catch (err: any) {
        console.log(err);
        toast.error("获取历史会议错误", err);
        // 获取失败时使用空数组
        setCompletedMeetings([]);
        
        // 检查是否是用户未创建的错误
        if (err.message?.includes("user not found") || err.message?.includes("用户不存在")) {
          // console.log("用户尚未创建，等待用户创建完成");
          return;
        }
        
        // 其他错误处理
        if (err.message?.includes("network") || err.message?.includes("Network")) {
          toast.error("网络连接错误，请检查网络连接");
          return;
        }
        
        // 记录其他类型的错误
        toast.error("获取历史会议时发生未知错误:", err);
      } finally {
        setIsLoadingCompleted(false);
      }
    };

    if(activeTab === "completed") {
      checkUserAndFetchHistory();
    }
  }, [refreshTrigger, user?.id, isUserActive, startDate, endDate]); // 添加startDate和endDate依赖，实现时间筛选实时更新
  

  // 根据当前活动标签和所有会议数据过滤会议列表
  useEffect(() => {
    // 根据当前活动的标签选择相应的会议列表
    const allMeetings = activeTab === "upcoming" ? upcomingMeetings : activeTab === "ongoing" ? ongoingMeetings : completedMeetings;
    
    // 过滤逻辑
    const filtered = allMeetings.filter(meeting => {
      // 没有搜索查询或者是参会者搜索(参会者搜索的结果直接从API获取)则继续日期筛选
      const matchesSearch = !searchQuery.trim() || filterType === "参会者" || 
        (filterType === "会议号" 
          ? meeting?.meetingId?.toLowerCase().replace(/\s+/g, '').startsWith(searchQuery.toLowerCase().replace(/\s+/g, ''))
          : meeting?.title?.toLowerCase().includes(searchQuery.toLowerCase()));
      
      // 如果设置了日期筛选，对所有类型会议进行日期过滤
      if ((startDate || endDate) && startDate !== endDate) {
        // 从会议开始时间中提取日期部分
        const meetingDate = meeting?.startTime?.split(' ')[0]; // 格式: YYYY-MM-DD
        
        // 检查会议日期是否在选定范围内
        const afterStartDate = !startDate || meetingDate >= startDate;
        const beforeEndDate = !endDate || meetingDate <= endDate;
        
        return matchesSearch && afterStartDate && beforeEndDate;
      }
      
      return matchesSearch;
    });

    // 排序会议
				function parseMeetingTime(str: string) {
					// 提取 "2025-06-06 14:00"
					return str.replace(/ 星期[一二三四五六日天]/, "");
				}

				const sortedMeetings = [...filtered].sort((a, b) => {
					const timeA =
						new Date(parseMeetingTime(a.startTime)).getTime() || 0;
					const timeB =
						new Date(parseMeetingTime(b.startTime)).getTime() || 0;
					return sortDirection === "asc"
						? timeA - timeB
						: timeB - timeA;
				});
    
    setFilteredMeetings(sortedMeetings);
  }, [activeTab, searchQuery, filterType, upcomingMeetings, ongoingMeetings, completedMeetings, sortDirection, startDate, endDate]);

  // 处理搜索
  const handleSearch = () => {
    // 如果是参会者搜索并且在已结束标签下
    if (filterType === "参会者" && activeTab === "completed" && searchQuery.trim()) {
      handleSearchParticipantMeetings();
    } else {
      // 已经通过useEffect实现了搜索过滤，这里可以添加额外逻辑
      toast.info(`执行搜索: ${filterType}=${searchQuery}`);
    }
  };

  // 根据参会者ID搜索会议
  const handleSearchParticipantMeetings = async () => {
    if (!searchQuery.trim()) {
      return;
    }
    
    setIsSearchingParticipant(true);
    setIsLoadingCompleted(true);
    
    try {
      const response = await fetch(`/api/meetings/history?userId=${searchQuery.trim()}&pageSize=20&page=1`);
      
      if (!response.ok) {
        throw new Error(`获取参会者会议失败: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || "获取参会者会议失败");
      }
      
      // 打印测试结果
      // console.log("参会者会议API返回数据:", data.data);
      
      // 将API返回的会议列表转换为Meeting类型
      if (data.data?.meeting_info_list?.length > 0) {
        const convertedMeetings = data.data.meeting_info_list.map((item: any) => formatMeetingData(item));
        
        // console.log("处理后的参会者会议数据:", convertedMeetings);
        setCompletedMeetings(convertedMeetings);
        
        // 直接更新过滤后的会议列表，确保立即显示结果
        // 过滤日期条件
        const filteredByDate = convertedMeetings.filter((meeting: Meeting) => {
          if (startDate || endDate) {
            // 从会议开始时间中提取日期部分
            const meetingDate = meeting.startTime?.split(' ')[0]; // 格式: YYYY-MM-DD
            
            // 检查会议日期是否在选定范围内
            const afterStartDate = !startDate || meetingDate >= startDate;
            const beforeEndDate = !endDate || meetingDate <= endDate;
            
            return afterStartDate && beforeEndDate;
          }
          return true;
        });
        
        // 排序会议
        const sortedMeetings = [...filteredByDate].sort((a, b) => {
          // 将日期字符串转换为毫秒时间戳进行比较
          const dateA = new Date(a.startTime?.split(' ')[0]?.replace(/-/g, '/'))?.getTime() || 0;
          const dateB = new Date(b.startTime?.split(' ')[0]?.replace(/-/g, '/'))?.getTime() || 0;
          
          // 根据排序方向返回比较结果
          return sortDirection === "asc" ? dateA - dateB : dateB - dateA;
        });
        
        setFilteredMeetings(sortedMeetings);
      } else {
        console.warn("参会者会议数据中未找到有效的meeting_info_list字段或数据为空");
        setCompletedMeetings([]);
        setFilteredMeetings([]);
      }
    } catch (error: any) {
      toast.error("搜索参会者会议错误:", error);
      setCompletedMeetings([]);
      setFilteredMeetings([]);
    } finally {
      setIsLoadingCompleted(false);
      setIsSearchingParticipant(false);
    }
  };

  // 处理回车搜索
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      // handleSearch();
    }
  };

  // 清除搜索
  const clearSearch = () => {
    
    setSearchQuery("");
    // 如果是参会者搜索，需要重新加载常规会议列表
    if (filterType === "参会者" && activeTab === "completed") {
      setRefreshTrigger(prev => prev + 1);
    }
  };

  /* 修改块开始: 取消会议API调用优化
   * 修改范围: 传入真实用户ID而非使用固定operatorId，增强错误处理
   * 对应需求: 使用user.id取消会议，提供更好的错误反馈
   * 恢复方法: 删除userId参数和用户检查，恢复原有的API调用
   */
  // 取消会议
  const handleCancelMeeting = async (meetingId: string) => {
    if (!meetingId) {
      return;
    }
    
    // 检查用户信息是否存在
    if (!user?.id) {
      toast.error("用户信息不存在，请重新登录");
      return;
    }
    
    if (!window.confirm("确定要取消这个会议吗？")) {
      return;
    }
    
    try {
      const response = await fetch(`/api/meetings/cancel/${meetingId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          reason_code: 1,
          userId: user.id // 传入真实用户ID
        })
      });
      
      const data = await response.json();
      
      if (!response.ok || !data.success) {
        // 增强错误处理，显示更详细的错误信息
        const errorMessage = data.error || 
                           `取消会议失败 (状态码: ${response.status})` ||
                           "取消会议失败";
        throw new Error(errorMessage);
      }
      
      // 立即从即将召开的会议列表中移除被取消的会议
      setUpcomingMeetings(prevMeetings => 
        prevMeetings.filter(meeting => meeting.id !== meetingId)
      );
      
      // 立即从进行中的会议列表中移除被取消的会议
      setOngoingMeetings(prevMeetings => 
        prevMeetings.filter(meeting => meeting.id !== meetingId)
      );
      
      // 更新过滤后的会议列表
      setFilteredMeetings(prevFiltered => 
        prevFiltered.filter(meeting => meeting.id !== meetingId)
      );
      
      toast.success("已成功取消会议");
      
      // 延迟1.5秒后刷新即将召开的会议列表和清除成功消息
      setTimeout(() => {
        setRefreshTrigger(prev => prev + 1);
      }, 1500);
      
    } catch (err: any) {
      // 增强错误处理，提供更友好的错误信息
      let errorMessage = "取消会议时发生错误";
      
      if (err.message) {
        errorMessage = err.message;
      } else if (err.name === 'TypeError' && err.message?.includes('fetch')) {
        errorMessage = "网络连接失败，请检查网络连接后重试";
      }
      toast.error("取消会议错误", err);
    }
  };
  /* 修改块结束: 取消会议API调用优化
   */

  // 处理排序方向切换
  const toggleSortDirection = () => {
    setSortDirection(prevDirection => prevDirection === "asc" ? "desc" : "asc");
  };

  // 处理开始日期变更
  const handleStartDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setStartDate(e.target.value);
    // 仅针对历史会议列表触发实时筛选
    if (activeTab === "completed") {
      setRefreshTrigger(prev => prev + 1);
    }
  };

  // 处理结束日期变更
  const handleEndDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEndDate(e.target.value);
    // 仅针对历史会议列表触发实时筛选
    if (activeTab === "completed") {
      setRefreshTrigger(prev => prev + 1);
    }
  };
  
  // 清除日期筛选
  const clearDateFilter = () => {
    const today = new Date();
    const oneMonthAgo = new Date(today);
    oneMonthAgo.setMonth(today.getMonth() - 1);
    const oneMonthLater = new Date(today);
    oneMonthLater.setMonth(today.getMonth() + 1);
    let startDateStr = "";
    let endDateStr = "";
    

    if (activeTab === "completed") {
      startDateStr = oneMonthAgo.toISOString().split("T")[0];
      endDateStr = today.toISOString().split("T")[0];
    } else {
      startDateStr = today.toISOString().split("T")[0];
      endDateStr = oneMonthLater.toISOString().split("T")[0];
    }

    setStartDate(startDateStr);
    setEndDate(endDateStr);
    setSearchQuery(""); // 清空搜索框
    // 如果是参会者搜索，需要重新加载常规会议列表
    if (filterType === "参会者" && activeTab === "completed") {
      setRefreshTrigger(prev => prev + 1);
    }
  };

  /* 修改块开始: 用户状态监控优化
   * 修改范围: 优化用户状态检查逻辑，避免阻塞已激活用户的体验
   * 对应需求: 直接展示会议列表，后台异步检查用户状态
   * 恢复方法: 删除此修改块内所有代码，恢复原有的同步检查逻辑
   */
  

  // 检查用户状态
  const checkUserStatus = async () => {
    if (!user) {
      return;
    }
    
    setIsCheckingUserStatus(true);
    // try {
      

    //   if (currentUser) {
    //     const isActive = currentUser.status === "1";
    //     setIsUserActive(isActive);
    //   } else {
    //     // console.log('未找到当前用户');
    //     setIsUserActive(false);
    //   }
    // } catch (error:any) {
    //   toast.error('检查用户状态失败:', error);
    //   // 检查失败时保持当前状态，不影响用户体验
    // } finally {
    //   setIsCheckingUserStatus(false);
    //   setHasCheckedUserStatus(true);
    // }
  };

  // 添加测试更新用户部门的函数
		// const UpdateDepartment = async (departmentId: string) => {
		//   if (!user?.id) {
		//     toast.error("用户信息不存在，请重新登录");
		//     return;
		//   }

		//   try {
		//     const response = await fetch(`/api/meetings/users/${user.id}`, {
		//       method: 'PUT',
		//       headers: {
		//         'Content-Type': 'application/json',
		//       },
		//       body: JSON.stringify({
		//         department_list: [departmentId]
		//       }),
		//     });

		//     const data = await response.json();

		//     if (!data.success) {
		//       throw new Error(data.error || "更新用户部门失败");
		//     }

		//     toast.success("更新用户部门成功");
		//     // 刷新用户列表
		//     await fetchUsers();
		//   } catch (error: any) {
		//     toast.error("更新用户部门失败:", error);
		//   }
		// };

		return (
			<div className="space-y-4">
				<div className="rounded-md overflow-hidden bg-card">
					{/* 选项卡 */}
					<div className="w-full">
						<div className="">
							<ul className="mt-2 flex list-none flex-row gap-6 ">
								<li>
									<button
										type="button"
										onClick={() => setActiveTab("upcoming")}
										className={cn(
											"flex items-center gap-2 border-b-2 py-1.5 text-sm",
											activeTab === "upcoming"
												? "border-primary font-bold"
												: "border-transparent",
										)}
									>
										即将开始
									</button>
								</li>
								<li>
									<button
										type="button"
										onClick={() => setActiveTab("ongoing")}
										className={cn(
											"flex items-center gap-2 border-b-2 py-1.5 text-sm",
											activeTab === "ongoing"
												? "border-primary font-bold"
												: "border-transparent",
										)}
									>
										进行中
									</button>
								</li>
								<li>
									<button
										type="button"
										onClick={() =>
											setActiveTab("completed")
										}
										className={cn(
											"flex items-center gap-2 border-b-2 py-1.5 text-sm",
											activeTab === "completed"
												? "border-primary font-bold"
												: "border-transparent",
										)}
									>
										已结束
									</button>
								</li>
							</ul>
						</div>

						{/* 搜索栏 */}
						<div className="flex items-center gap-2 flex-wrap py-4">
							{/* 修改人：Miya，修改日期：2025/7/22 */}
							{/* 修改说明：优化会议类型筛选器的布局和样式 */}
							<div className="flex-1 flex items-center gap-2 flex-wrap ml-0.5">
								{/* 修改人：Miya，修改日期：2025/7/22 */}
								{/* 修改说明：会议类型选择器，支持筛选不同状态的会议 */}
								<Select
									value={filterType}
									onValueChange={setFilterType}
									disabled={!isUserActive}
								>
									{/* 修改人：Miya，修改日期：2025/7/22 */}
									{/* 修改说明：添加hideIcon={false}属性，确保显示下拉图标 */}
									{/* 说明：Select组件已支持hideIcon属性，false表示显示图标，true表示隐藏图标 */}
									<SelectTrigger
										className="h-9 w-[120px] text-sm"
										hideIcon={false}
									>
										<SelectValue placeholder="筛选条件" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem
											value="会议号"
											className="text-sm"
										>
											会议号
										</SelectItem>
										<SelectItem
											value="会议主题"
											className="text-sm"
										>
											会议主题
										</SelectItem>
										<SelectItem
											value="参会者"
											className="text-sm"
										>
											参会者
										</SelectItem>
									</SelectContent>
								</Select>

								<div className="relative w-64">
									<div className="relative flex items-center w-64">
										<Input
											placeholder={
												filterType === "参会者"
													? "请输入参会者ID"
													: `请输入${filterType}`
											}
											value={searchQuery}
											onChange={(e) =>
												setSearchQuery(e.target.value)
											}
											className="h-9 pr-20 text-sm"
											onKeyDown={handleKeyDown}
											disabled={!isUserActive}
										/>
										<div>
											{searchQuery && (
												<button
													className="absolute right-2 top-1/2 transform -translate-y-1/2 h-5 w-5 flex items-center justify-center text-muted-foreground hover:text-foreground"
													onClick={clearSearch}
													title="清除搜索"
													type="button"
													disabled={!isUserActive}
												>
													<XIcon className="size-4" />
												</button>
											)}
											{/* {searchQuery && (
												<button
													className="absolute right-1 top-1/2 transform -translate-y-1/2 h-5 w-5 flex items-center justify-center text-muted-foreground hover:text-foreground"
													onClick={handleSearch}
													title="搜索"
													type="button"
													disabled={!isUserActive}
												>
													<Search className="size-4" />
												</button>
											)} */}
										</div>
									</div>
								</div>

								<DateFilter
									startDate={startDate}
									endDate={endDate}
									handleStartDateChange={
										handleStartDateChange
									}
									handleEndDateChange={handleEndDateChange}
									clearDateFilter={clearDateFilter}
									disabled={!isUserActive}
								/>

								

                {/* <button
									className="justify-center border font-medium enabled:cursor-pointer transition-colors focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:pointer-events-none disabled:opacity-50 [&>svg+svg]:hidden size-9 [&>svg]:m-0 [&>svg]:opacity-100 w-9 flex items-center gap-1.5 shrink-0 text-sm h-9 rounded-md bg-background text-foreground border-border hover:bg-accent"
									// onClick={clearDateFilter}
									title="搜索"
									type="button"
									disabled={!isUserActive}
								>
									<SearchIcon className="size-4" />
								</button> */}
                
								<button
									className="justify-center border font-medium enabled:cursor-pointer transition-colors focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:pointer-events-none disabled:opacity-50 [&>svg+svg]:hidden size-9 [&>svg]:m-0 [&>svg]:opacity-100 w-9 flex items-center gap-1.5 shrink-0 text-sm h-9 rounded-md bg-background text-foreground border-border hover:bg-accent"
									onClick={clearDateFilter}
									title="清除所有筛选条件"
									type="button"
									disabled={!isUserActive}
								>
									<XIcon className="size-4" />
								</button>

								{isSearchingParticipant && (
									<div className="flex items-center text-sm text-primary">
										<div className="inline-block animate-spin rounded-full h-3 w-3 border-b-2 border-current mr-1" />
										正在查询参会者会议...
									</div>
								)}
							</div>

							<button
								// className="h-8 w-8 flex items-center justify-center border border-border p-1 ml-3 hover:bg-accent hover:text-accent-foreground"
								className="justify-center border font-medium enabled:cursor-pointer transition-colors focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:pointer-events-none disabled:opacity-50 [&>svg+svg]:hidden size-9 [&>svg]:m-0 [&>svg]:opacity-100 w-9 flex items-center gap-1.5 shrink-0 text-sm h-9 rounded-md bg-background text-foreground border-border hover:bg-accent"
								onClick={() =>
									setRefreshTrigger((prev) => prev + 1)
								}
								title="刷新会议列表"
								type="button"
								disabled={!isUserActive}
							>
								<RefreshCcwIcon className="size-4" />
							</button>

							<Link
								href={`/app/${organizationSlug}/meeting/list/schedule`}
								title="预定会议"
								className="justify-center border font-medium enabled:cursor-pointer transition-colors focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:pointer-events-none disabled:opacity-50 [&>svg+svg]:hidden size-9 [&>svg]:m-0 [&>svg]:opacity-100 w-9 flex items-center gap-1.5 shrink-0 text-sm h-9 rounded-md bg-background text-foreground border-border hover:bg-accent"
							>
								<CalendarIcon className="size-4" />
							</Link>
						</div>

						{/* 表格内容区域 */}
						{activeTab === "upcoming" && (
							<MeetingTable
								meetings={filteredMeetings}
								organizationSlug={organizationSlug}
								sortDirection={sortDirection}
								toggleSortDirection={toggleSortDirection}
								isLoading={isLoadingUpcoming}
								handleCancelMeeting={handleCancelMeeting}
								hasCheckedUserStatus={hasCheckedUserStatus}
								isUserActive={isUserActive}
								isActivating={isActivating}
								handleActivation={handleActivation}
								showActivationDialog={showActivationDialog}
								clearActivationCheck={clearActivationCheck}
							/>
						)}

						{activeTab === "ongoing" && (
							<MeetingTable
								meetings={filteredMeetings}
								organizationSlug={organizationSlug}
								sortDirection={sortDirection}
								toggleSortDirection={toggleSortDirection}
								isLoading={isLoadingOngoing}
								handleCancelMeeting={handleCancelMeeting}
								hasCheckedUserStatus={hasCheckedUserStatus}
								isUserActive={isUserActive}
								isActivating={isActivating}
								handleActivation={handleActivation}
								showActivationDialog={showActivationDialog}
								clearActivationCheck={clearActivationCheck}
							/>
						)}

						{activeTab === "completed" && (
							<MeetingTable
								meetings={filteredMeetings}
								organizationSlug={organizationSlug}
								sortDirection={sortDirection}
								toggleSortDirection={toggleSortDirection}
								isLoading={isLoadingCompleted}
								showCancelOption={false}
								timeColumnLabel="开始时间"
								showDocAndRecordButtons={true}
								hasCheckedUserStatus={hasCheckedUserStatus}
								isUserActive={isUserActive}
								isActivating={isActivating}
								handleActivation={handleActivation}
								showActivationDialog={showActivationDialog}
								clearActivationCheck={clearActivationCheck}
							/>
						)}
					</div>
				</div>
			</div>
		);
} 