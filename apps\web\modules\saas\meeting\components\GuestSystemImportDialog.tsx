"use client";

import { useState, use<PERSON>em<PERSON>, use<PERSON><PERSON>back, useRef, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@ui/components/dialog";
import {
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Button } from "@ui/components/button";
import { Database, Users, Search, X, Trash2 } from "lucide-react";
import type { GuestData } from "../utils/guestExcelParser";
import { ActiveOrganizationContext } from "../../organizations/lib/active-organization-context";
import { toast } from "sonner";
import { Table, ConfigProvider } from "antd";
import type { TableColumnType } from "antd";

import zhCN from "antd/lib/locale/zh_CN";
import React from "react";
import { AntMeetingTable } from "@saas/meeting/components/ant-meeting-table";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>ider,
  Toolt<PERSON><PERSON>rigger,
  TooltipPortal
} from "@ui/components/tooltip";
import {
  useGuestShareholders,
  type SelectableShareholderData,
  type ShareholderData
} from "../hooks/useGuestShareholders";
import { PaginationStatus } from "./PaginationStatus";

/**
 * 渲染带有Tooltip的截断文本
 * @param text 要显示的文本
 * @param maxLength 最大文本长度，超过则截断并显示tooltip
 * @returns 截断后的文本组件或原文本
 */
function renderTruncatedWithTooltip(text: string | undefined | null, maxLength = 15) {
  // 如果文本为空、undefined或null，返回占位符
  if (text === undefined || text === null) {
    return "-";
  }

  if (text === "") {
    return "-";
  }

  // 确保text是字符串类型
  const textStr = String(text);

  // 使用更智能的判断：只有当文本实际超出maxLength时才截断
  const shouldTruncate = textStr.length > maxLength;

  if (!shouldTruncate) {
    return textStr;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <span className="cursor-default truncate inline-block max-w-full">
            {textStr.slice(0, maxLength)}...
          </span>
        </TooltipTrigger>
        <TooltipPortal>
          <TooltipContent side="top" align="start" className="max-w-[300px]">
            {textStr}
          </TooltipContent>
        </TooltipPortal>
      </Tooltip>
    </TooltipProvider>
  );
}
interface GuestSystemImportDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onImportComplete: (guests: GuestData[]) => void;
  currentGuestCount: number;
}

/**
 * 嘉宾系统导入对话框
 * 采用左右双面板布局设计，参考MeetingFileUpload.tsx的实现模式
 */
export function GuestSystemImportDialog({
  isOpen,
  onOpenChange,
  onImportComplete,
}: GuestSystemImportDialogProps) {
  const activeOrganizationContext = React.useContext(ActiveOrganizationContext);
  const orgId = activeOrganizationContext?.activeOrganization?.id;

  // 使用新的分页Hook
  const {
    shareholders,
    pagination,
    isLoading,
    isLoadingMore,
    error,
    searchTerm,
    selectedShareholders,
    selectedCount,
    loadMore,
    handleSearch,
    resetSearch,
    resetSearchKeepData,
    handleShareholderSelect,
    handleBatchSelect,
    clearAllSelections,
  } = useGuestShareholders(orgId || "", {
    enabled: !!orgId, // 移除isOpen条件，允许数据持久化
    pageSize: 20
  });

  // 已确认的股东列表（右侧面板）
  const [confirmedShareholders, setConfirmedShareholders] = useState<SelectableShareholderData[]>([]);
  const [rightCurrentPage, setRightCurrentPage] = useState(1);
  const [isRightScrollLoading, setIsRightScrollLoading] = useState(false);

  // 滚动相关引用
  const scrollRef = useRef({
    left: 0,
    top: 0,
  });
  const leftTableScrollRef = useRef<HTMLDivElement>(null);
  const rightTableScrollRef = useRef<HTMLDivElement>(null);

  // 当前显示的股东列表（Hook已经处理了搜索和分页）
  const displayedShareholders = useMemo(() => {
    // 将 SelectableShareholderData 转换为兼容 Meeting 类型的对象
    const result = shareholders.map(shareholder => ({
      ...shareholder,
      id: shareholder.id, // 使用 id 作为 id
    }));


    return result;
  }, [shareholders, pagination?.total]);

  // 是否还有更多数据
  const hasMoreData = useMemo(() => {
    return pagination?.hasNext || false;
  }, [pagination?.hasNext]);

  // 右侧表格的分页显示逻辑
  const rightPageSize = 20;
  const displayedConfirmedShareholders = useMemo(() => {
    const endIndex = rightCurrentPage * rightPageSize;
    const result = confirmedShareholders.slice(0, endIndex);

    return result.map(shareholder => ({
      ...shareholder,
      id: shareholder.id,
    }));
  }, [confirmedShareholders, rightCurrentPage]);

  // 右侧表格是否还有更多数据
  const rightHasMoreData = useMemo(() => {
    return rightCurrentPage * rightPageSize < confirmedShareholders.length;
  }, [rightCurrentPage, confirmedShareholders.length]);



  // 处理对话框关闭
  const handleClose = () => {
    if (!isLoading) {
      onOpenChange(false);
      // 只清空右侧已选股东，保留左侧股东数据和搜索状态
      handleClearConfirmedShareholders();
      // 清空左侧选择状态，但保留数据
      clearAllSelections();
    }
  };

  // 处理全选/取消全选
  const handleSelectAll = useCallback((isSelected: boolean) => {
    // 只影响当前显示的股东数据，而不是所有数据
    const displayedIds = displayedShareholders.map(s => s.id);
    handleBatchSelect(displayedIds, isSelected);
  }, [displayedShareholders, handleBatchSelect]);

  // 计算全选状态
  const selectAllState = useMemo(() => {
    const visibleShareholders = displayedShareholders;
    if (visibleShareholders.length === 0) return { checked: false, indeterminate: false };

    const selectedCount = visibleShareholders.filter((s: any) => s.isSelected).length;
    if (selectedCount === 0) return { checked: false, indeterminate: false };
    if (selectedCount === visibleShareholders.length) return { checked: true, indeterminate: false };
    return { checked: false, indeterminate: true };
  }, [displayedShareholders]);

  // 处理左侧表格滚动事件
  const handleLeftTableScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, clientHeight, scrollHeight, scrollLeft } = event.target as HTMLDivElement;

    // 记录滚动容器引用
    if (!leftTableScrollRef.current && event.target) {
      leftTableScrollRef.current = event.target as HTMLDivElement;
    }

    // 垂直滚动 & 到底了 & 有更多数据 & 不在加载中
    if (
      Math.abs(scrollTop - scrollRef.current.top) > 0 &&
      scrollTop + clientHeight >= scrollHeight - 50 && // 提前50px触发加载
      hasMoreData &&
      !isLoadingMore &&
      !isLoading
    ) {
      // 调用Hook提供的loadMore函数
      loadMore();
    }

    // 记录当前滚动信息
    scrollRef.current = {
      left: scrollLeft,
      top: scrollTop,
    };
  }, [hasMoreData, isLoadingMore, isLoading, loadMore]);

  // 处理右侧表格滚动事件
  const handleRightTableScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, clientHeight, scrollHeight, scrollLeft } = event.target as HTMLDivElement;

    // 记录滚动容器引用
    if (!rightTableScrollRef.current && event.target) {
      rightTableScrollRef.current = event.target as HTMLDivElement;
    }

    // 垂直滚动 & 到底了 & 有更多数据 & 不在加载中
    if (
      Math.abs(scrollTop - scrollRef.current.top) > 0 &&
      scrollTop + clientHeight >= scrollHeight - 50 && // 提前50px触发加载
      rightHasMoreData &&
      !isRightScrollLoading
    ) {
      // 设置滚动加载状态
      setIsRightScrollLoading(true);

      // 加载下一页
      setRightCurrentPage(prev => prev + 1);

      // 延迟重置加载状态，避免频繁触发
      setTimeout(() => {
        setIsRightScrollLoading(false);
      }, 500);
    }

    // 记录当前滚动信息
    scrollRef.current = {
      left: scrollLeft,
      top: scrollTop,
    };
  }, [rightHasMoreData, isRightScrollLoading]);

  // 左侧表格滚动配置
  const leftScrollConfig = useMemo(() => {
    // 智能垂直滚动：只有当数据超过10行时才启用垂直滚动，避免少量数据时出现滚动条
    const shouldEnableYScroll = displayedShareholders.length > 10;

    return {
      x: "max-content", // 启用水平滚动
      y: shouldEnableYScroll ? "calc(90vh - 350px)" : undefined, // 根据数据量决定是否启用垂直滚动
      scrollToFirstRowOnChange: false, // 设置为false以减少滚动指示器的显示
    };
  }, [displayedShareholders.length]);

  // 右侧表格滚动配置
  const rightScrollConfig = useMemo(() => {
    // 智能垂直滚动：只有当数据超过10行时才启用垂直滚动，避免少量数据时出现滚动条
    const shouldEnableYScroll = displayedConfirmedShareholders.length > 10;

    return {
      x: "max-content", // 启用水平滚动
      y: shouldEnableYScroll ? "calc(90vh - 350px)" : undefined, // 根据数据量决定是否启用垂直滚动
      scrollToFirstRowOnChange: false, // 设置为false以减少滚动指示器的显示
    };
  }, [displayedConfirmedShareholders.length]);

  // 清空左侧选择
  const handleClearLeftSelection = useCallback(() => {
    // 只清空当前显示的股东数据的选择状态，而不是所有数据
    const displayedIds = displayedShareholders.map(s => s.id);
    handleBatchSelect(displayedIds, false);
  }, [displayedShareholders, handleBatchSelect]);

  // 确认选择，将选中的股东移动到右侧
  const handleConfirmSelection = useCallback(() => {
    if (selectedShareholders.length === 0) {
      // toast.warning("请先选择要添加的股东");
      return;
    }

    setConfirmedShareholders(prev => {
      // 避免重复添加
      const existingIds = prev.map(s => s.id);
      const newShareholders = selectedShareholders.filter(s => !existingIds.includes(s.id));
      const duplicateCount = selectedShareholders.length - newShareholders.length;

      if (duplicateCount > 0) {
        // toast.warning(`已跳过 ${duplicateCount} 位重复的股东`);
      }

      if (newShareholders.length > 0) {
        // toast.success(`成功添加 ${newShareholders.length} 位股东到待导入列表`);
      }

      return [...prev, ...newShareholders];
    });

    // 重置右侧分页到第一页
    setRightCurrentPage(1);

    // 清空左侧选择
    clearAllSelections();
  }, [selectedShareholders, clearAllSelections]);

  // 从右侧移除单个股东
  const handleRemoveConfirmedShareholder = useCallback((id: string) => {
    setConfirmedShareholders(prev =>
      prev.filter(shareholder => shareholder.id !== id)
    );
  }, []);

  // 清空右侧所有股东
  const handleClearConfirmedShareholders = useCallback(() => {
    setConfirmedShareholders([]);
  }, []);

  // 最终导入股东数据
  const handleFinalImport = useCallback(() => {
    if (confirmedShareholders.length === 0) {
      // toast.warning("请先选择要导入的股东");
      return;
    }

    try {
      // 转换为GuestData格式
      const guestData: GuestData[] = confirmedShareholders.map(shareholder => ({
        area: "86", // 默认添加区号86
        phone_number: shareholder.contactNumber,
        guest_name: shareholder.securitiesAccountName
      }));

      // 调用导入完成回调
      onImportComplete(guestData);
      // toast.success(`成功导入 ${guestData.length} 位股东到会议嘉宾列表`);
      handleClose();
    } catch (error: any) {
      toast.error("导入股东数据失败，请重试");
      console.error("导入股东数据失败:", error);
    }
  }, [confirmedShareholders, onImportComplete]);

  // Hook会自动处理数据获取，不需要手动调用



  // 格式化期数日期显示
  const formatRegisterDate = (dateString: string) => {
    if (!dateString) return "-";
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return dateString;
      return date.toISOString().split('T')[0]; // 返回 YYYY-MM-DD 格式
    } catch (error) {
      return dateString;
    }
  };



  // 左侧股东表格的columns配置
  const leftTableColumns: TableColumnType<SelectableShareholderData>[] = useMemo(() => [
    {
      title: () => (
        <input
          type="checkbox"
          checked={selectAllState.checked}
          ref={(input) => {
            if (input) input.indeterminate = selectAllState.indeterminate;
          }}
          onChange={(e) => handleSelectAll(e.target.checked)}
          className="h-4 w-4"
        />
      ),
      dataIndex: "isSelected",
      key: "isSelected",
      width: 50, // 减小宽度
      render: (_, record) => (
        <input
          type="checkbox"
          checked={record.isSelected}
          onChange={(e) => handleShareholderSelect(record.id, e.target.checked)}
          className="h-4 w-4" // 减小复选框大小
        />
      ),
    },
    {
      title: "股东名字",
      dataIndex: "securitiesAccountName",
      key: "securitiesAccountName",
      width: 120, // 参考 ShareholderTable 的宽度设置
      className: "text-center font-medium", // 参考 ShareholderTable 的样式
      render: (name: string) =>
        renderTruncatedWithTooltip(String(name), 15), // 使用 ShareholderTable 的截断方式
    },
    {
      title: "联系方式",
      dataIndex: "contactNumber",
      key: "contactNumber",
      width: 90, // 减小宽度
      ellipsis: true, // 启用省略号
      render: (contact: string) => (
        <div className="text-xs text-muted-foreground truncate" title={contact}>
          {contact}
        </div>
      ),
    },
    {
      title: "期数",
      dataIndex: "registerDate",
      key: "registerDate",
      width: 80, // 设置宽度
      ellipsis: true, // 启用省略号
      render: (registerDate: string) => (
        <div className="text-xs text-muted-foreground truncate" title={formatRegisterDate(registerDate)}>
          {formatRegisterDate(registerDate)}
        </div>
      ),
    },
  ], [handleShareholderSelect, handleSelectAll, selectAllState, formatRegisterDate]);

  // 右侧已选股东表格的columns配置
  const rightTableColumns: TableColumnType<SelectableShareholderData>[] = useMemo(() => [
    {
      title: "股东名字",
      dataIndex: "securitiesAccountName",
      key: "securitiesAccountName",
      width: 120, // 参考 ShareholderTable 的宽度设置
      className: "text-center font-medium", // 参考 ShareholderTable 的样式
      render: (name: string) =>
        renderTruncatedWithTooltip(String(name), 15), // 使用 ShareholderTable 的截断方式
    },
    {
      title: "联系方式",
      dataIndex: "contactNumber",
      key: "contactNumber",
      width: 90, // 减小宽度
      ellipsis: true, // 启用省略号
      render: (contact: string) => (
        <div className="text-xs text-muted-foreground truncate" title={contact}>
          {contact}
        </div>
      ),
    },
    {
      title: "期数",
      dataIndex: "registerDate",
      key: "registerDate",
      width: 80, // 设置宽度
      ellipsis: true, // 启用省略号
      render: (registerDate: string) => (
        <div className="text-xs text-muted-foreground truncate" title={formatRegisterDate(registerDate)}>
          {formatRegisterDate(registerDate)}
        </div>
      ),
    },
    {
      title: "操作",
      key: "action",
      width: 50,
      ellipsis: true,
      render: (_, record: SelectableShareholderData) => (
        <div className="flex justify-center items-center">
          <Button
            onClick={() => handleRemoveConfirmedShareholder(record.id)}
            variant="ghost"
            size="icon"
            className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      ),
    },
  ], [handleRemoveConfirmedShareholder, formatRegisterDate]);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-7xl w-[100vw] h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0 pb-4">
          <DialogTitle>系统导入成员</DialogTitle>
          <DialogDescription>
          可从系统联系人或数据源导入成员，仅支持手机号
          </DialogDescription>
        </DialogHeader>

        {/* 双面板布局 - 使用固定高度和独立滚动 */}
        <div className="flex flex-col xl:flex-row gap-4 flex-1 min-h-0 py-2">
          {/* 左侧面板 - 股东列表 */}
          <div className="flex-1 xl:w-1/2 min-h-0 flex flex-col">
            <CardContent className="flex-1 flex flex-col min-h-0 p-0 pb-8">
              {/* 搜索和筛选区域 - 固定高度 */}
              <div className="flex-shrink-0 mb-12">
                <div className="flex gap-6">
                  <select 
                    className="flex relative items-center justify-center text-muted-foreground hover:text-foreground"
                    tabIndex={-1}
                    onFocus={(e) => e.target.blur()}
                  >
                    <option value="1">股东</option>
                  </select>
                  {/* 搜索框 */}
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="搜索股东姓名或联系方式..."
                      value={searchTerm}
                      onChange={(e) => handleSearch(e.target.value)}
                      className="pl-10"
                      disabled={isLoading}
                    />
                    {/* 添加删除按钮，在已有输入的情况下展示，点击后可删除输入 */}
                    {searchTerm && (
                      <button
                        type="button"
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 flex items-center justify-center text-muted-foreground hover:text-foreground"
                        onClick={() => resetSearch()}
                        title="清除搜索"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </div>

                {/* 搜索结果统计 */}
                {searchTerm && (
                  <div className="px-6 pb-2 text-sm text-muted-foreground">
                    {isLoading ? (
                      "正在搜索..."
                    ) : (
                      `找到 ${pagination?.total || 0} 条匹配结果`
                    )}
                  </div>
                )}
              </div>

              {/* 错误提示 */}
              {error && (
                <div className="flex-shrink-0 mb-4 p-4 bg-destructive/10 border border-destructive/20 rounded-md">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-destructive">
                      <X className="h-4 w-4" />
                      <span className="text-sm">
                        {error.message || "加载股东数据失败"}
                      </span>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.location.reload()}
                      className="text-destructive border-destructive hover:bg-destructive hover:text-destructive-foreground"
                    >
                      重试
                    </Button>
                  </div>
                </div>
              )}

              {/* 股东表格 - 设置固定高度确保一致性 */}
              <div className="flex-1 min-h-[calc(60vh-200px)]">
                <AntMeetingTable
                  columns={leftTableColumns}
                  data={displayedShareholders}
                  emptyContent={
                    error
                      ? "加载失败，请重试"
                      : isLoading
                        ? "正在加载股东数据..."
                        : "暂无股东数据"
                  }
                  loading={isLoading || isLoadingMore}
                  onScroll={handleLeftTableScroll}
                  scroll={leftScrollConfig}
                />
                
                <PaginationStatus
                currentLoaded={displayedShareholders.length}
                total={pagination?.total || 0}
                isLoading={isLoading && displayedShareholders.length === 0}
                className="justify-end flex-shrink-0 "
                />
              </div>
              
              {/* 分页状态显示 */}
              
            </CardContent>
            
            {/* 左侧操作按钮 - 固定在底部 */}
            <div className="flex-shrink-0 flex justify-between">
              <Button
                variant="outline"
                onClick={handleClearLeftSelection}
                disabled={selectedCount === 0 || isLoading}
              >
                清空
              </Button>
              <Button
                onClick={handleConfirmSelection}
                disabled={selectedCount === 0 || isLoading}
              >
                确认选择 ({selectedCount})
              </Button>
            </div>
          </div>

          {/* 右侧面板 - 已选股东管理 */}
          <div className="flex-1 xl:w-1/2 min-h-0 flex flex-col">
            <CardContent className="flex-1 flex flex-col min-h-0 p-0 pb-8">
              {/* 标题区域 - 固定高度，与左侧搜索区域高度保持一致 */}
              <div className="flex-shrink-0 mb-12  flex items-center justify-end">
                <span className="text-lg text-muted-foreground text-bold h-9">已选</span>
              </div>

              {/* 已选股东表格 - 与左侧表格使用相同的固定高度 */}
              <div className="flex-1 min-h-[calc(60vh-200px)]">
                <AntMeetingTable
                  data={displayedConfirmedShareholders}
                  columns={rightTableColumns}
                  emptyContent={<div className="h-[calc(60vh-200px)] flex items-center justify-center text-muted-foreground">从左侧选择股东后，已选股东将显示在这里</div>}
                  loading={isRightScrollLoading}
                  onScroll={handleRightTableScroll}
                  scroll={rightScrollConfig}
                />
                {/* 分页状态显示 - 与左侧保持一致 字体居中*/}
              <PaginationStatus
                currentLoaded={displayedConfirmedShareholders.length}
                total={confirmedShareholders.length || 0}
                isLoading={isLoading && displayedConfirmedShareholders.length === 0}
                className="flex-shrink-0"
              />
              </div>
              
              
            </CardContent>
            
            {/* 右侧操作按钮 - 固定在底部 */}
            <div className="flex-shrink-0 flex justify-between  mt-3">
              <Button
                variant="outline"
                onClick={handleClearConfirmedShareholders}
                disabled={confirmedShareholders.length === 0}
              >
                清空
              </Button>
              <Button
                onClick={handleFinalImport}
                disabled={confirmedShareholders.length === 0}
                className="bg-primary hover:bg-primary/90"
              >
                确认导入 ({confirmedShareholders.length})
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}









